package com.app.wordifynumbers.data

import android.content.Context
// Temporary import from stubs while Room KSP issue is resolved
// import androidx.room.Database
// import androidx.room.Room
// import androidx.room.RoomDatabase
// import androidx.room.TypeConverters
// import androidx.sqlite.db.SupportSQLiteDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.Executors

/**
 * Room database for finance entries
 */
@Database(entities = [SimpleFinanceEntry::class], version = 1, exportSchema = false)
@TypeConverters(Converters::class)
abstract class SimpleFinanceDatabase : RoomDatabase() {

    abstract fun financeEntryDao(): SimpleFinanceEntryDao

    companion object {
        @Volatile
        private var INSTANCE: SimpleFinanceDatabase? = null

        fun getDatabase(context: Context): SimpleFinanceDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = Room.databaseBuilder(
                    context.applicationContext,
                    SimpleFinanceDatabase::class,
                    "simple_finance_database"
                )
                .fallbackToDestructiveMigration()
                .addCallback(SimpleDatabaseCallback())
                .build()

                INSTANCE = instance
                instance
            }
        }

        /**
         * Callback for database creation
         */
        private class SimpleDatabaseCallback : RoomDatabase.Callback() {
            fun onCreate(db: SupportSQLiteDatabase) {
                // Database created, no sample data needed for production
            }
        }
    }
}

/**
 * Type converters for Room
 */
class Converters {
    @androidx.room.TypeConverter
    fun fromEntryType(value: EntryType): String {
        return value.name
    }

    @androidx.room.TypeConverter
    fun toEntryType(value: String): EntryType {
        return EntryType.valueOf(value)
    }
}
