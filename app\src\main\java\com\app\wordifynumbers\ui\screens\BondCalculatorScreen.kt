package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil
import com.app.wordifynumbers.util.FinancialCalculator
import com.app.wordifynumbers.util.BondResult
import kotlin.math.*

@Composable
fun BondCalculatorScreen(modifier: Modifier = Modifier) {
    // Animated neon glass background
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(NeonGlow.copy(alpha = 0.10f), NeonCard),
                    center = Offset(0.5f, 0.5f),
                    radius = 900f
                )
            )
    ) {
        Column(
            modifier = Modifier.fillMaxSize(),
            verticalArrangement = Arrangement.SpaceBetween
        ) {
            // Top Bar
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 20.dp, start = 16.dp, end = 16.dp, bottom = 6.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Bond Calculator",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.ExtraBold,
                        color = NeonGlow,
                        letterSpacing = 1.5.sp
                    )
                )
                IconButton(onClick = { /* TODO: Add help or settings */ }) {
                    Icon(Icons.Default.Info, contentDescription = "Info", tint = NeonGlow)
                }
            }
            // Main content
            Column(
                modifier = modifier
                    .fillMaxWidth()
                    .padding(horizontal = 12.dp)
                    .background(NeonCard.copy(alpha = 0.98f), RoundedCornerShape(22.dp))
                    .shadow(14.dp, RoundedCornerShape(22.dp)),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.spacedBy(18.dp)
            ) {
                var faceValue by remember { mutableStateOf("") }
                var couponRate by remember { mutableStateOf("") }
                var marketPrice by remember { mutableStateOf("") }
                var yearsToMaturity by remember { mutableStateOf("") }
                var selectedPaymentFrequency by remember { mutableStateOf(BondPaymentFrequency.SEMI_ANNUAL) }
                var selectedBondType by remember { mutableStateOf(BondType.FIXED_RATE) }
                var result by remember { mutableStateOf<BondResult?>(null) }
                var showAdvancedOptions by remember { mutableStateOf(false) }
                var referenceRate by remember { mutableStateOf("") } // For floating rate bonds
                var inflationRate by remember { mutableStateOf("") } // For inflation-linked bonds
                var conversionRatio by remember { mutableStateOf("") } // For convertible bonds

                val context = LocalContext.current

                // Bond Type Selection
                NeonCard {
                    Column(
                        modifier = Modifier.padding(18.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        Text(
                            text = "Bond Type",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonGlow
                        )

                        // Bond Type Selection
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            BondType.values().take(3).forEach { bondType ->
                                NeonButton(
                                    onClick = {
                                        selectedBondType = bondType
                                        result = null
                                    },
                                    modifier = Modifier.weight(1f),
                                    enabled = selectedBondType != bondType
                                ) {
                                    Text(
                                        text = bondType.displayName,
                                        style = MaterialTheme.typography.labelMedium
                                    )
                                }
                            }
                        }

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            BondType.values().drop(3).forEach { bondType ->
                                NeonButton(
                                    onClick = {
                                        selectedBondType = bondType
                                        result = null
                                    },
                                    modifier = Modifier.weight(1f),
                                    enabled = selectedBondType != bondType
                                ) {
                                    Text(
                                        text = bondType.displayName,
                                        style = MaterialTheme.typography.labelMedium
                                    )
                                }
                            }
                            // Empty space for alignment
                            Spacer(modifier = Modifier.weight(1f))
                        }
                    }
                }

                // Input Section
                NeonCard {
                    Column(
                        modifier = Modifier.padding(18.dp),
                        verticalArrangement = Arrangement.spacedBy(10.dp)
                    ) {
                        Text(
                            text = "Bond Details",
                            style = MaterialTheme.typography.titleMedium,
                            color = NeonGlow
                        )

                        NumberInputField(
                            value = faceValue,
                            onValueChange = { value: String ->
                                faceValue = value
                                result = null
                            },
                            label = "Face Value",
                            validation = NumberValidation.POSITIVE,
                            helpText = "Par value of the bond"
                        )

                        // Coupon rate is not applicable for zero-coupon bonds
                        if (selectedBondType != BondType.ZERO_COUPON) {
                            NumberInputField(
                                value = couponRate,
                                onValueChange = { value: String ->
                                    couponRate = value
                                    result = null
                                },
                                label = "Coupon Rate (%)",
                                validation = NumberValidation.PERCENTAGE,
                                helpText = when(selectedBondType) {
                                    BondType.FIXED_RATE -> "Annual interest rate"
                                    BondType.FLOATING_RATE -> "Spread over reference rate"
                                    BondType.INFLATION_LINKED -> "Real interest rate"
                                    BondType.CONVERTIBLE -> "Annual interest rate"
                                    else -> ""
                                }
                            )
                        }

                        NumberInputField(
                            value = marketPrice,
                            onValueChange = { value: String ->
                                marketPrice = value
                                result = null
                            },
                            label = "Market Price",
                            validation = NumberValidation.POSITIVE,
                            helpText = "Current bond price"
                        )

                        NumberInputField(
                            value = yearsToMaturity,
                            onValueChange = { value: String ->
                                yearsToMaturity = value
                                result = null
                            },
                            label = "Years to Maturity",
                            validation = NumberValidation.POSITIVE,
                            helpText = "Number of years until maturity"
                        )

                        // Payment frequency selection
                        Text(
                            text = "Payment Frequency",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonText
                        )

                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            BondPaymentFrequency.values().forEach { frequency ->
                                NeonButton(
                                    onClick = {
                                        selectedPaymentFrequency = frequency
                                        result = null
                                    },
                                    modifier = Modifier.weight(1f),
                                    enabled = selectedPaymentFrequency != frequency
                                ) {
                                    Text(
                                        text = frequency.displayName,
                                        style = MaterialTheme.typography.labelSmall
                                    )
                                }
                            }
                        }

                        // Advanced options toggle
                        NeonButton(
                            onClick = { showAdvancedOptions = !showAdvancedOptions },
                            modifier = Modifier.fillMaxWidth()
                        ) {
                            Text(
                                text = if (showAdvancedOptions) "Hide Advanced Options" else "Show Advanced Options",
                                style = MaterialTheme.typography.labelMedium
                            )
                        }

                        // Advanced options
                        AnimatedVisibility(visible = showAdvancedOptions) {
                            Column(
                                modifier = Modifier.fillMaxWidth(),
                                verticalArrangement = Arrangement.spacedBy(10.dp)
                            ) {
                                when (selectedBondType) {
                                    BondType.FLOATING_RATE -> {
                                        NumberInputField(
                                            value = referenceRate,
                                            onValueChange = { value: String ->
                                                referenceRate = value
                                                result = null
                                            },
                                            label = "Reference Rate (%)",
                                            validation = NumberValidation.PERCENTAGE,
                                            helpText = "Base rate (e.g., LIBOR, SOFR)"
                                        )
                                    }
                                    BondType.INFLATION_LINKED -> {
                                        NumberInputField(
                                            value = inflationRate,
                                            onValueChange = { value: String ->
                                                inflationRate = value
                                                result = null
                                            },
                                            label = "Inflation Rate (%)",
                                            validation = NumberValidation.PERCENTAGE,
                                            helpText = "Expected annual inflation rate"
                                        )
                                    }
                                    BondType.CONVERTIBLE -> {
                                        NumberInputField(
                                            value = conversionRatio,
                                            onValueChange = { value: String ->
                                                conversionRatio = value
                                                result = null
                                            },
                                            label = "Conversion Ratio",
                                            validation = NumberValidation.POSITIVE,
                                            helpText = "Number of shares per bond"
                                        )
                                    }
                                    else -> {}
                                }
                            }
                        }
                    }
                }
                // Calculate button
                NeonButton(
                    onClick = {
                        result = try {
                            val fv = faceValue.toDoubleOrNull() ?: return@NeonButton
                            val mp = marketPrice.toDoubleOrNull() ?: return@NeonButton
                            val ytm = yearsToMaturity.toDoubleOrNull() ?: return@NeonButton
                            val ppy = selectedPaymentFrequency.paymentsPerYear

                            // Different calculation based on bond type
                            when (selectedBondType) {
                                BondType.FIXED_RATE -> {
                                    val cr = couponRate.toDoubleOrNull() ?: return@NeonButton
                                    FinancialCalculator.calculateBondYield(
                                        faceValue = fv,
                                        couponRate = cr,
                                        marketPrice = mp,
                                        yearsToMaturity = ytm,
                                        paymentsPerYear = ppy
                                    )
                                }
                                BondType.ZERO_COUPON -> {
                                    // For zero-coupon bonds, calculate yield based on discount from face value
                                    val ytmRate = (Math.pow(fv / mp, 1.0 / ytm) - 1) * 100
                                    val totalInterest = fv - mp

                                    BondResult(
                                        faceValue = fv,
                                        couponRate = 0.0,
                                        yearsToMaturity = ytm,
                                        marketRate = ytmRate,
                                        bondPrice = mp,
                                        currentYield = 0.0,
                                        yieldToMaturity = ytmRate,
                                        totalReturn = fv
                                    )
                                }
                                BondType.FLOATING_RATE -> {
                                    val spread = couponRate.toDoubleOrNull() ?: return@NeonButton
                                    val baseRate = referenceRate.toDoubleOrNull() ?: return@NeonButton
                                    val effectiveRate = baseRate + spread

                                    // Floating rate bonds typically trade close to par
                                    val currentYield = effectiveRate * fv / mp
                                    val couponPayment = fv * effectiveRate / 100 / ppy
                                    val totalInterest = couponPayment * ppy * ytm

                                    // Floating rate bonds have low duration due to rate resets
                                    val estimatedDuration = 1.0 / ppy // Approximate duration as time to next reset

                                    BondResult(
                                        faceValue = fv,
                                        couponRate = effectiveRate,
                                        yearsToMaturity = ytm,
                                        marketRate = effectiveRate,
                                        bondPrice = mp,
                                        currentYield = currentYield,
                                        yieldToMaturity = effectiveRate,
                                        totalReturn = fv + totalInterest
                                    )
                                }
                                BondType.INFLATION_LINKED -> {
                                    val realRate = couponRate.toDoubleOrNull() ?: return@NeonButton
                                    val inflation = inflationRate.toDoubleOrNull() ?: return@NeonButton

                                    // Fisher equation: nominal rate ≈ real rate + inflation rate
                                    val nominalRate = realRate + inflation + (realRate * inflation / 100)
                                    val adjustedFaceValue = fv * Math.pow(1 + inflation/100, ytm)
                                    val couponPayment = fv * realRate / 100 / ppy
                                    val inflationAdjustedCoupon = couponPayment * Math.pow(1 + inflation/100, ytm/2)
                                    val totalInterest = (couponPayment * ppy * ytm) + (adjustedFaceValue - fv)

                                    BondResult(
                                        faceValue = fv,
                                        couponRate = realRate,
                                        yearsToMaturity = ytm,
                                        marketRate = nominalRate,
                                        bondPrice = mp,
                                        currentYield = realRate * fv / mp,
                                        yieldToMaturity = nominalRate,
                                        totalReturn = adjustedFaceValue
                                    )
                                }
                                BondType.CONVERTIBLE -> {
                                    val cr = couponRate.toDoubleOrNull() ?: return@NeonButton
                                    val ratio = conversionRatio.toDoubleOrNull() ?: return@NeonButton

                                    // Basic bond calculations
                                    val standardBond = FinancialCalculator.calculateBondYield(
                                        faceValue = fv,
                                        couponRate = cr,
                                        marketPrice = mp,
                                        yearsToMaturity = ytm,
                                        paymentsPerYear = ppy
                                    )

                                    // Conversion option value (simplified)
                                    val conversionValue = fv * ratio / 100
                                    val optionValue = Math.max(0.0, conversionValue - mp)

                                    // Adjust yield for conversion option
                                    val adjustedYield = if (optionValue > 0) {
                                        standardBond.yieldToMaturity * (1 - optionValue / mp)
                                    } else {
                                        standardBond.yieldToMaturity
                                    }

                                    BondResult(
                                        faceValue = fv,
                                        couponRate = cr,
                                        yearsToMaturity = ytm,
                                        marketRate = adjustedYield,
                                        bondPrice = mp,
                                        currentYield = standardBond.currentYield,
                                        yieldToMaturity = adjustedYield,
                                        totalReturn = fv + standardBond.totalReturn + optionValue
                                    )
                                }
                            }
                        } catch (e: Exception) {
                            FeedbackUtil.errorFeedback(context)
                            null
                        }
                    },
                    modifier = Modifier.fillMaxWidth()
                ) { Text("Calculate") }

                // Results
                AnimatedVisibility(result != null) {
                    NeonCard(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 8.dp)
                    ) {
                        if (result != null) {
                            Column(
                                modifier = Modifier.padding(18.dp),
                                verticalArrangement = Arrangement.spacedBy(12.dp)
                            ) {
                                // Basic yield information
                                Text(
                                    text = "Yield Information",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonGlow
                                )

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text("Yield to Maturity:", color = NeonText)
                                    Text("${"%.2f".format(result?.yieldToMaturity ?: 0.0)}%", color = NeonGlow)
                                }

                                if (selectedBondType != BondType.ZERO_COUPON) {
                                    Row(
                                        modifier = Modifier.fillMaxWidth(),
                                        horizontalArrangement = Arrangement.SpaceBetween
                                    ) {
                                        Text("Current Yield:", color = NeonText)
                                        Text("${"%.2f".format(result?.currentYield ?: 0.0)}%", color = NeonGlow)
                                    }
                                }

                                Divider(color = NeonGlow.copy(alpha = 0.3f))

                                // Payment information
                                Text(
                                    text = "Payment Information",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonGlow
                                )

                                if (selectedBondType != BondType.ZERO_COUPON) {
                                // Removed coupon payment and annual payment rows
                                }

                                Row(
                                    modifier = Modifier.fillMaxWidth(),
                                    horizontalArrangement = Arrangement.SpaceBetween
                                ) {
                                    Text("Total Return:", color = NeonText)
                                    Text("$${"%.2f".format(result?.totalReturn ?: 0.0)}", color = NeonGlow)
                                }

                                Divider(color = NeonGlow.copy(alpha = 0.3f))

                                // Risk metrics
                                Text(
                                    text = "Risk Metrics",
                                    style = MaterialTheme.typography.titleMedium,
                                    color = NeonGlow
                                )

                                // Removed duration, modified duration, and convexity rows

                                // Bond type specific information
                                when (selectedBondType) {
                                    BondType.ZERO_COUPON -> {
                                        Divider(color = NeonGlow.copy(alpha = 0.3f))
                                        Text(
                                            text = "Zero-Coupon Bond Information",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = NeonGlow
                                        )
                                        Text(
                                            text = "This bond does not pay periodic interest. Instead, it is sold at a discount to face value and redeemed at maturity for full face value.",
                                            color = NeonText
                                        )
                                    }
                                    BondType.FLOATING_RATE -> {
                                        Divider(color = NeonGlow.copy(alpha = 0.3f))
                                        Text(
                                            text = "Floating Rate Bond Information",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = NeonGlow
                                        )
                                        Text(
                                            text = "This bond's coupon rate adjusts periodically based on a reference rate plus a spread. It typically has lower interest rate risk than fixed-rate bonds.",
                                            color = NeonText
                                        )
                                    }
                                    BondType.INFLATION_LINKED -> {
                                        Divider(color = NeonGlow.copy(alpha = 0.3f))
                                        Text(
                                            text = "Inflation-Linked Bond Information",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = NeonGlow
                                        )
                                        Text(
                                            text = "This bond's principal value adjusts with inflation, providing protection against inflation risk. The coupon payments are based on the adjusted principal.",
                                            color = NeonText
                                        )
                                    }
                                    BondType.CONVERTIBLE -> {
                                        Divider(color = NeonGlow.copy(alpha = 0.3f))
                                        Text(
                                            text = "Convertible Bond Information",
                                            style = MaterialTheme.typography.titleMedium,
                                            color = NeonGlow
                                        )
                                        Text(
                                            text = "This bond can be converted into a predetermined number of shares of the issuer's common stock. It typically offers lower coupon rates due to the conversion option.",
                                            color = NeonText
                                        )
                                    }
                                    else -> {}
                                }
                            }
                        } else {
                            Text(
                                text = "Please enter valid numbers for all fields.",
                                style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.SemiBold),
                                color = NeonRed
                            )
                        }
                    }
                }
            }
            // Footer
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(bottom = 12.dp),
                contentAlignment = Alignment.Center
            ) {
                Text(
                    text = "Made with  499 by Wordify Numbers",
                    style = MaterialTheme.typography.labelLarge.copy(
                        color = NeonGlow.copy(alpha = 0.7f),
                        fontWeight = FontWeight.Medium
                    )
                )
            }
        }
    }
}

@Composable
private fun BondIndicator(
    label: String,
    icon: ImageVector,
    color: Color,
    details: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Icon(
            imageVector = icon,
            contentDescription = null,
            tint = color
        )
        Column {
            Text(
                text = label,
                color = color,
                style = MaterialTheme.typography.titleSmall
            )
            Text(
                text = details,
                color = NeonText.copy(alpha = 0.7f),
                style = MaterialTheme.typography.bodySmall
            )
        }
    }
}

private enum class BondPaymentFrequency(val displayName: String, val paymentsPerYear: Int) {
    ANNUAL("Annual", 1),
    SEMI_ANNUAL("Semi-Annual", 2),
    QUARTERLY("Quarterly", 4),
    MONTHLY("Monthly", 12)
}

private enum class BondType(val displayName: String) {
    FIXED_RATE("Fixed Rate"),
    ZERO_COUPON("Zero Coupon"),
    FLOATING_RATE("Floating Rate"),
    INFLATION_LINKED("Inflation-Linked"),
    CONVERTIBLE("Convertible")
}

private fun Double.format(digits: Int) = "%.${digits}f".format(this)
