package com.app.wordifynumbers.ui.screens

import androidx.activity.compose.BackHandler
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.tween
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.platform.LocalContext
import androidx.activity.compose.BackHandler
import com.app.wordifynumbers.ui.components.CalculatorInputField
import com.app.wordifynumbers.ui.components.NeonCard
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.util.FeedbackUtil
import kotlin.math.*

// Data classes for calculator logic
data class Shape(
    val id: String,
    val name: String,
    val icon: ImageVector,
    val category: ShapeCategory,
    val description: String,
    val is3D: Boolean = false
)

enum class ShapeCategory(val displayName: String, val color: Color) {
    BASIC_2D("Basic 2D", NeonBlue),
    ADVANCED_2D("Advanced 2D", NeonGreen),
    BASIC_3D("Basic 3D", NeonPurple),
    ADVANCED_3D("Advanced 3D", NeonOrange)
}

enum class MeasurementUnit(val displayName: String, val symbol: String) {
    MILLIMETER("Millimeter", "mm"),
    CENTIMETER("Centimeter", "cm"),
    METER("Meter", "m"),
    KILOMETER("Kilometer", "km"),
    INCH("Inch", "in"),
    FOOT("Foot", "ft"),
    YARD("Yard", "yd")
}

data class CalculationResult(
    val shape: Shape,
    val unit: MeasurementUnit,
    val area: Double? = null,
    val volume: Double? = null,
    val perimeter: Double? = null,
    val surfaceArea: Double? = null,
    val formattedResults: Map<String, String> = emptyMap()
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AreaVolumeCalculatorScreen(
    modifier: Modifier = Modifier
) {
    val context = LocalContext.current
    var selectedShape by remember { mutableStateOf(getAvailableShapes().first()) }
    var selectedUnit by remember { mutableStateOf(MeasurementUnit.METER) }
    var input1 by remember { mutableStateOf("") }
    var input2 by remember { mutableStateOf("") }
    var input3 by remember { mutableStateOf("") }
    var calculationResult by remember { mutableStateOf<CalculationResult?>(null) }
    var showResult by remember { mutableStateOf(false) }

    // Handle back button press
    BackHandler {
        // Let the MainScreen handle the back press
        // This will navigate back to calculator selection
        FeedbackUtil.buttonPress(context)
    }

    LazyColumn(
        modifier = modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(NeonBackground, NeonBackground.copy(alpha = 0.8f))
                )
            )
            .padding(16.dp),
        verticalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        // Header
        item {
            HeaderSection()
        }

        // Shape Selection
        item {
            ShapeSelectionSection(
                selectedShape = selectedShape,
                onShapeSelected = { 
                    selectedShape = it
                    input1 = ""
                    input2 = ""
                    input3 = ""
                    showResult = false
                }
            )
        }

        // Unit Selection
        item {
            UnitSelectionSection(
                selectedUnit = selectedUnit,
                onUnitSelected = { selectedUnit = it }
            )
        }

        // Input Section
        item {
            InputSection(
                shape = selectedShape,
                unit = selectedUnit,
                input1 = input1,
                input2 = input2,
                input3 = input3,
                onInput1Change = { input1 = it },
                onInput2Change = { input2 = it },
                onInput3Change = { input3 = it }
            )
        }

        // Calculate Button
        item {
            CalculateButton(
                onClick = {
                    val result = calculateResult(selectedShape, selectedUnit, input1, input2, input3)
                    calculationResult = result
                    showResult = result != null
                }
            )
        }

        // Results Section
        item {
            AnimatedVisibility(
                visible = showResult && calculationResult != null,
                enter = fadeIn(animationSpec = tween(500)),
                exit = fadeOut(animationSpec = tween(300))
            ) {
                calculationResult?.let { result ->
                    ResultSection(result = result)
                }
            }
        }

        // Footer
        item {
            FooterSection()
        }
    }
}

@Composable
private fun HeaderSection() {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Icon(
                imageVector = Icons.Default.Calculate,
                contentDescription = null,
                tint = NeonGlow,
                modifier = Modifier.size(48.dp)
            )
            Spacer(modifier = Modifier.height(12.dp))
            Text(
                text = "Area & Volume Calculator",
                style = MaterialTheme.typography.headlineMedium.copy(
                    fontWeight = FontWeight.Bold,
                    color = NeonText
                ),
                textAlign = TextAlign.Center
            )
            Text(
                text = "Professional geometric calculations",
                style = MaterialTheme.typography.bodyMedium.copy(
                    color = NeonText.copy(alpha = 0.7f)
                ),
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun ShapeSelectionSection(
    selectedShape: Shape,
    onShapeSelected: (Shape) -> Unit
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Select Shape",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = NeonBlue
                )
            )

            // Category tabs
            val categories = ShapeCategory.values()
            var selectedCategory by remember { mutableStateOf(selectedShape.category) }

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(categories) { category ->
                    Button(
                        onClick = { selectedCategory = category },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (selectedCategory == category) category.color.copy(alpha = 0.3f) else NeonCard,
                            contentColor = if (selectedCategory == category) category.color else NeonText.copy(alpha = 0.7f)
                        ),
                        shape = RoundedCornerShape(20.dp),
                        modifier = Modifier.height(36.dp)
                    ) {
                        Text(
                            text = category.displayName,
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }

            // Shape selection for selected category
            val shapesInCategory = getAvailableShapes().filter { it.category == selectedCategory }

            LazyColumn(
                modifier = Modifier.height(200.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(shapesInCategory) { shape ->
                    ShapeCard(
                        shape = shape,
                        isSelected = shape.id == selectedShape.id,
                        onClick = { onShapeSelected(shape) }
                    )
                }
            }
        }
    }
}

@Composable
private fun ShapeCard(
    shape: Shape,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) shape.category.color.copy(alpha = 0.1f) else NeonCard
        ),
        border = BorderStroke(
            width = if (isSelected) 2.dp else 1.dp,
            color = if (isSelected) shape.category.color else NeonText.copy(alpha = 0.2f)
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.spacedBy(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = shape.icon,
                contentDescription = null,
                tint = if (isSelected) shape.category.color else NeonText.copy(alpha = 0.7f),
                modifier = Modifier.size(24.dp)
            )

            Column(
                modifier = Modifier.weight(1f),
                verticalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Text(
                    text = shape.name,
                    style = MaterialTheme.typography.titleSmall.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = if (isSelected) shape.category.color else NeonText
                )
                Text(
                    text = shape.description,
                    style = MaterialTheme.typography.bodySmall,
                    color = NeonText.copy(alpha = 0.7f)
                )
                Text(
                    text = if (shape.is3D) "3D Shape" else "2D Shape",
                    style = MaterialTheme.typography.bodySmall.copy(
                        fontWeight = FontWeight.Medium
                    ),
                    color = if (shape.is3D) NeonPurple else NeonBlue
                )
            }

            if (isSelected) {
                Icon(
                    imageVector = Icons.Default.CheckCircle,
                    contentDescription = "Selected",
                    tint = shape.category.color,
                    modifier = Modifier.size(20.dp)
                )
            }
        }
    }
}

@Composable
private fun UnitSelectionSection(
    selectedUnit: MeasurementUnit,
    onUnitSelected: (MeasurementUnit) -> Unit
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            Text(
                text = "Measurement Unit",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = NeonGreen
                )
            )

            LazyRow(
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                items(MeasurementUnit.values()) { unit ->
                    Button(
                        onClick = { onUnitSelected(unit) },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = if (selectedUnit == unit) NeonGreen.copy(alpha = 0.3f) else NeonCard,
                            contentColor = if (selectedUnit == unit) NeonGreen else NeonText.copy(alpha = 0.7f)
                        ),
                        shape = RoundedCornerShape(20.dp),
                        modifier = Modifier.height(36.dp)
                    ) {
                        Text(
                            text = "${unit.displayName} (${unit.symbol})",
                            style = MaterialTheme.typography.bodyMedium
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun InputSection(
    shape: Shape,
    unit: MeasurementUnit,
    input1: String,
    input2: String,
    input3: String,
    onInput1Change: (String) -> Unit,
    onInput2Change: (String) -> Unit,
    onInput3Change: (String) -> Unit
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Text(
                text = "Enter Dimensions",
                style = MaterialTheme.typography.titleLarge.copy(
                    fontWeight = FontWeight.Bold,
                    color = NeonPurple
                )
            )

            when (shape.id) {
                "rectangle" -> {
                    CalculatorInputField(
                        label = "Length (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = {
                            Text(
                                text = "Enter the length of the rectangle",
                                color = NeonText.copy(alpha = 0.8f),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    )
                    CalculatorInputField(
                        label = "Width (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = {
                            Text(
                                text = "Enter the width of the rectangle",
                                color = NeonText.copy(alpha = 0.8f),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    )
                }
                "square" -> {
                    CalculatorInputField(
                        label = "Side Length (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = {
                            Text(
                                text = "Enter the side length of the square",
                                color = NeonText.copy(alpha = 0.8f),
                                style = MaterialTheme.typography.bodySmall
                            )
                        }
                    )
                }
                "circle" -> {
                    CalculatorInputField(
                        label = "Radius (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the radius of the circle") }
                    )
                }
                "triangle" -> {
                    CalculatorInputField(
                        label = "Base (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the base length of the triangle") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the height of the triangle") }
                    )
                }
                "cube" -> {
                    CalculatorInputField(
                        label = "Side Length (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the side length of the cube") }
                    )
                }
                "sphere" -> {
                    CalculatorInputField(
                        label = "Radius (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the radius of the sphere") }
                    )
                }
                "cylinder" -> {
                    CalculatorInputField(
                        label = "Radius (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the radius of the cylinder") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the height of the cylinder") }
                    )
                }
                "cone" -> {
                    CalculatorInputField(
                        label = "Radius (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the radius of the cone base") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { SupportingText("Enter the height of the cone") }
                    )
                }
                "ellipse" -> {
                    CalculatorInputField(
                        label = "Semi-major Axis (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the semi-major axis length") }
                    )
                    CalculatorInputField(
                        label = "Semi-minor Axis (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the semi-minor axis length") }
                    )
                }
                "trapezoid" -> {
                    CalculatorInputField(
                        label = "Base 1 (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the length of the first base") }
                    )
                    CalculatorInputField(
                        label = "Base 2 (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the length of the second base") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input3,
                        onValueChange = onInput3Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the height of the trapezoid") }
                    )
                }
                "parallelogram" -> {
                    CalculatorInputField(
                        label = "Base (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the base length") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the height") }
                    )
                }
                "rhombus" -> {
                    CalculatorInputField(
                        label = "Diagonal 1 (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the first diagonal length") }
                    )
                    CalculatorInputField(
                        label = "Diagonal 2 (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the second diagonal length") }
                    )
                }
                "pyramid" -> {
                    CalculatorInputField(
                        label = "Base Side (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the side length of the square base") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the height of the pyramid") }
                    )
                }
                "prism" -> {
                    CalculatorInputField(
                        label = "Length (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the length") }
                    )
                    CalculatorInputField(
                        label = "Width (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the width") }
                    )
                    CalculatorInputField(
                        label = "Height (${unit.symbol})",
                        value = input3,
                        onValueChange = onInput3Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the height") }
                    )
                }
                "torus" -> {
                    CalculatorInputField(
                        label = "Major Radius (${unit.symbol})",
                        value = input1,
                        onValueChange = onInput1Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the major radius (center to tube center)") }
                    )
                    CalculatorInputField(
                        label = "Minor Radius (${unit.symbol})",
                        value = input2,
                        onValueChange = onInput2Change,
                        accentColor = NeonPurple,
                        supportingText = { Text("Enter the minor radius (tube radius)") }
                    )
                }
            }
        }
    }
}

@Composable
private fun CalculateButton(
    onClick: () -> Unit
) {
    Button(
        onClick = onClick,
        modifier = Modifier
            .fillMaxWidth()
            .height(56.dp),
        colors = ButtonDefaults.buttonColors(
            containerColor = NeonGlow,
            contentColor = NeonBackground
        ),
        shape = RoundedCornerShape(16.dp)
    ) {
        Row(
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = Icons.Default.Calculate,
                contentDescription = null,
                modifier = Modifier.size(24.dp)
            )
            Text(
                text = "Calculate",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                fontSize = 18.sp
            )
        }
    }
}

@Composable
private fun ResultSection(
    result: CalculationResult
) {
    NeonCard(
        modifier = Modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(24.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(12.dp)
            ) {
                Icon(
                    imageVector = Icons.Default.Analytics,
                    contentDescription = null,
                    tint = NeonGlow,
                    modifier = Modifier.size(32.dp)
                )
                Text(
                    text = "Calculation Results",
                    style = MaterialTheme.typography.headlineSmall.copy(
                        fontWeight = FontWeight.Bold,
                        color = NeonGlow
                    )
                )
            }

            Divider(
                color = NeonGlow.copy(alpha = 0.3f),
                thickness = 1.dp
            )

            // Shape info
            Text(
                text = "Shape: ${result.shape.name}",
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Medium,
                    color = NeonText
                )
            )

            // Results
            result.formattedResults.forEach { (key, value) ->
                ResultRow(label = key, value = value)
            }
        }
    }
}

@Composable
private fun ResultRow(
    label: String,
    value: String
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = label,
            style = MaterialTheme.typography.bodyLarge.copy(
                color = NeonText.copy(alpha = 0.8f)
            )
        )
        Text(
            text = value,
            style = MaterialTheme.typography.bodyLarge.copy(
                fontWeight = FontWeight.Bold,
                color = NeonGlow
            )
        )
    }
}

@Composable
private fun FooterSection() {
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 16.dp),
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = "Made with ❤️ by Wordify Numbers",
            style = MaterialTheme.typography.bodyMedium.copy(
                color = NeonText.copy(alpha = 0.6f),
                fontWeight = FontWeight.Medium
            ),
            textAlign = TextAlign.Center
        )
    }
}

// Helper functions
@Composable
private fun SupportingText(text: String) {
    Text(
        text = text,
        color = NeonText.copy(alpha = 0.8f),
        style = MaterialTheme.typography.bodySmall
    )
}

fun getAvailableShapes(): List<Shape> {
    return listOf(
        // Basic 2D Shapes
        Shape(
            id = "rectangle",
            name = "Rectangle",
            icon = Icons.Default.CropLandscape,
            category = ShapeCategory.BASIC_2D,
            description = "Four-sided shape with opposite sides equal"
        ),
        Shape(
            id = "square",
            name = "Square",
            icon = Icons.Default.CropSquare,
            category = ShapeCategory.BASIC_2D,
            description = "Four-sided shape with all sides equal"
        ),
        Shape(
            id = "circle",
            name = "Circle",
            icon = Icons.Default.Circle,
            category = ShapeCategory.BASIC_2D,
            description = "Round shape with constant radius"
        ),
        Shape(
            id = "triangle",
            name = "Triangle",
            icon = Icons.Default.ChangeHistory,
            category = ShapeCategory.BASIC_2D,
            description = "Three-sided polygon"
        ),

        // Advanced 2D Shapes
        Shape(
            id = "ellipse",
            name = "Ellipse",
            icon = Icons.Default.Lens,
            category = ShapeCategory.ADVANCED_2D,
            description = "Oval shape with two radii"
        ),
        Shape(
            id = "trapezoid",
            name = "Trapezoid",
            icon = Icons.Default.CropPortrait,
            category = ShapeCategory.ADVANCED_2D,
            description = "Four-sided shape with one pair of parallel sides"
        ),
        Shape(
            id = "parallelogram",
            name = "Parallelogram",
            icon = Icons.Default.CropLandscape,
            category = ShapeCategory.ADVANCED_2D,
            description = "Four-sided shape with opposite sides parallel"
        ),
        Shape(
            id = "rhombus",
            name = "Rhombus",
            icon = Icons.Default.CropRotate,
            category = ShapeCategory.ADVANCED_2D,
            description = "Four-sided shape with all sides equal"
        ),

        // Basic 3D Shapes
        Shape(
            id = "cube",
            name = "Cube",
            icon = Icons.Default.ViewInAr,
            category = ShapeCategory.BASIC_3D,
            description = "Six-faced shape with all sides equal",
            is3D = true
        ),
        Shape(
            id = "sphere",
            name = "Sphere",
            icon = Icons.Default.SportsBasketball,
            category = ShapeCategory.BASIC_3D,
            description = "Round 3D shape with constant radius",
            is3D = true
        ),
        Shape(
            id = "cylinder",
            name = "Cylinder",
            icon = Icons.Default.ViewColumn,
            category = ShapeCategory.BASIC_3D,
            description = "Circular base with height",
            is3D = true
        ),
        Shape(
            id = "cone",
            name = "Cone",
            icon = Icons.Default.Terrain,
            category = ShapeCategory.BASIC_3D,
            description = "Circular base tapering to a point",
            is3D = true
        ),

        // Advanced 3D Shapes
        Shape(
            id = "pyramid",
            name = "Pyramid",
            icon = Icons.Default.ChangeHistory,
            category = ShapeCategory.ADVANCED_3D,
            description = "Square base tapering to a point",
            is3D = true
        ),
        Shape(
            id = "prism",
            name = "Rectangular Prism",
            icon = Icons.Default.ViewInAr,
            category = ShapeCategory.ADVANCED_3D,
            description = "Box shape with length, width, and height",
            is3D = true
        ),
        Shape(
            id = "torus",
            name = "Torus",
            icon = Icons.Default.DonutLarge,
            category = ShapeCategory.ADVANCED_3D,
            description = "Donut shape with major and minor radius",
            is3D = true
        )
    )
}

fun calculateResult(
    shape: Shape,
    unit: MeasurementUnit,
    input1: String,
    input2: String,
    input3: String
): CalculationResult? {
    val value1 = input1.toDoubleOrNull() ?: return null
    val value2 = input2.toDoubleOrNull()
    val value3 = input3.toDoubleOrNull()

    return when (shape.id) {
        "rectangle" -> {
            if (value2 == null) return null
            val area = value1 * value2
            val perimeter = 2 * (value1 + value2)
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                perimeter = perimeter,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol),
                    "Perimeter" to "%.2f %s".format(perimeter, unit.symbol)
                )
            )
        }
        "square" -> {
            val area = value1 * value1
            val perimeter = 4 * value1
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                perimeter = perimeter,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol),
                    "Perimeter" to "%.2f %s".format(perimeter, unit.symbol)
                )
            )
        }
        "circle" -> {
            val area = PI * value1 * value1
            val circumference = 2 * PI * value1
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                perimeter = circumference,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol),
                    "Circumference" to "%.2f %s".format(circumference, unit.symbol)
                )
            )
        }
        "triangle" -> {
            if (value2 == null) return null
            val area = 0.5 * value1 * value2
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol)
                )
            )
        }
        "cube" -> {
            val volume = value1 * value1 * value1
            val surfaceArea = 6 * value1 * value1
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "sphere" -> {
            val volume = (4.0/3.0) * PI * value1 * value1 * value1
            val surfaceArea = 4 * PI * value1 * value1
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "cylinder" -> {
            if (value2 == null) return null
            val volume = PI * value1 * value1 * value2
            val surfaceArea = 2 * PI * value1 * (value1 + value2)
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "cone" -> {
            if (value2 == null) return null
            val volume = (1.0/3.0) * PI * value1 * value2 * value1
            val slantHeight = sqrt(value1 * value1 + value2 * value2)
            val surfaceArea = PI * value1 * (value1 + slantHeight)
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "ellipse" -> {
            if (value2 == null) return null
            val area = PI * value1 * value2
            val perimeter = PI * (3 * (value1 + value2) - sqrt((3 * value1 + value2) * (value1 + 3 * value2)))
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                perimeter = perimeter,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol),
                    "Perimeter (approx)" to "%.2f %s".format(perimeter, unit.symbol)
                )
            )
        }
        "trapezoid" -> {
            if (value2 == null || value3 == null) return null
            val area = 0.5 * (value1 + value2) * value3
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol)
                )
            )
        }
        "parallelogram" -> {
            if (value2 == null) return null
            val area = value1 * value2
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol)
                )
            )
        }
        "rhombus" -> {
            if (value2 == null) return null
            val area = 0.5 * value1 * value2
            CalculationResult(
                shape = shape,
                unit = unit,
                area = area,
                formattedResults = mapOf(
                    "Area" to "%.2f %s²".format(area, unit.symbol)
                )
            )
        }
        "pyramid" -> {
            if (value2 == null) return null
            val volume = (1.0/3.0) * value1 * value1 * value2
            val slantHeight = sqrt((value1/2) * (value1/2) + value2 * value2)
            val surfaceArea = value1 * value1 + 2 * value1 * slantHeight
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "prism" -> {
            if (value2 == null || value3 == null) return null
            val volume = value1 * value2 * value3
            val surfaceArea = 2 * (value1 * value2 + value2 * value3 + value1 * value3)
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        "torus" -> {
            if (value2 == null) return null
            val volume = 2 * PI * PI * value2 * value2 * value1
            val surfaceArea = 4 * PI * PI * value1 * value2
            CalculationResult(
                shape = shape,
                unit = unit,
                volume = volume,
                surfaceArea = surfaceArea,
                formattedResults = mapOf(
                    "Volume" to "%.2f %s³".format(volume, unit.symbol),
                    "Surface Area" to "%.2f %s²".format(surfaceArea, unit.symbol)
                )
            )
        }
        else -> null
    }
}
