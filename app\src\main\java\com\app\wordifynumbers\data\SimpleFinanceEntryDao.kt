package com.app.wordifynumbers.data

// Temporary import from stubs while Room KSP issue is resolved
// import androidx.room.Dao
// import androidx.room.Delete
// import androidx.room.Insert
// import androidx.room.OnConflictStrategy
// import androidx.room.Query
// import androidx.room.Update
import kotlinx.coroutines.flow.Flow

/**
 * Data Access Object for SimpleFinanceEntry
 */
@Dao
interface SimpleFinanceEntryDao {

    /**
     * Insert a new entry
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(entry: SimpleFinanceEntry)

    /**
     * Update an existing entry
     */
    @Update
    suspend fun update(entry: SimpleFinanceEntry)

    /**
     * Delete an entry
     */
    @Delete
    suspend fun delete(entry: SimpleFinanceEntry)

    /**
     * Delete an entry by ID
     */
    @Query("DELETE FROM finance_entries WHERE id = :id")
    suspend fun deleteById(id: String)

    /**
     * Get all entries, ordered by date (newest first)
     */
    @Query("SELECT * FROM finance_entries ORDER BY date DESC")
    fun getAllEntries(): Flow<List<SimpleFinanceEntry>>

    /**
     * Get entries by type
     */
    @Query("SELECT * FROM finance_entries WHERE type = :type ORDER BY date DESC")
    fun getEntriesByType(type: EntryType): Flow<List<SimpleFinanceEntry>>

    /**
     * Get entries by category
     */
    @Query("SELECT * FROM finance_entries WHERE category = :category ORDER BY date DESC")
    fun getEntriesByCategory(category: String): Flow<List<SimpleFinanceEntry>>

    /**
     * Search entries by note
     */
    @Query("SELECT * FROM finance_entries WHERE note LIKE '%' || :query || '%' ORDER BY date DESC")
    fun searchEntries(query: String): Flow<List<SimpleFinanceEntry>>

    /**
     * Get entries within a date range
     */
    @Query("SELECT * FROM finance_entries WHERE date BETWEEN :startDate AND :endDate ORDER BY date DESC")
    fun getEntriesInDateRange(startDate: Long, endDate: Long): Flow<List<SimpleFinanceEntry>>

    /**
     * Get total income
     */
    @Query("SELECT SUM(amount) FROM finance_entries WHERE type = 'INCOME'")
    fun getTotalIncome(): Flow<Double?>

    /**
     * Get total expenses
     */
    @Query("SELECT SUM(amount) FROM finance_entries WHERE type = 'EXPENSE'")
    fun getTotalExpense(): Flow<Double?>

    /**
     * Get total income for a specific date range
     */
    @Query("SELECT SUM(amount) FROM finance_entries WHERE type = 'INCOME' AND date BETWEEN :startDate AND :endDate")
    fun getTotalIncomeInDateRange(startDate: Long, endDate: Long): Flow<Double?>

    /**
     * Get total expenses for a specific date range
     */
    @Query("SELECT SUM(amount) FROM finance_entries WHERE type = 'EXPENSE' AND date BETWEEN :startDate AND :endDate")
    fun getTotalExpenseInDateRange(startDate: Long, endDate: Long): Flow<Double?>

    /**
     * Delete all entries
     */
    @Query("DELETE FROM finance_entries")
    suspend fun deleteAllEntries()
}
