package com.app.wordifynumbers.data

import androidx.compose.runtime.snapshots.SnapshotStateList
import com.app.wordifynumbers.ui.viewmodel.TaskData

object TaskRepository {
    private val tasks = mutableListOf<TaskData>()

    fun getTasks(): List<TaskData> = tasks.toList()

    fun addTask(task: TaskData) {
        tasks.add(task)
    }

    fun updateTask(index: Int, task: TaskData) {
        if (index in tasks.indices) tasks[index] = task
    }

    fun removeTask(index: Int) {
        if (index in tasks.indices) tasks.removeAt(index)
    }

    fun clearAll() {
        tasks.clear()
    }
}
