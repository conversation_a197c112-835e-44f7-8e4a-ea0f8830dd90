package com.app.wordifynumbers.ui.navigation

import androidx.activity.compose.BackHandler
import androidx.compose.runtime.*
import androidx.compose.ui.platform.LocalContext
import com.app.wordifynumbers.util.FeedbackUtil

/**
 * A comprehensive back navigation handler that provides standardized back button behavior
 * across all screens in the Wordify Numbers app.
 */

/**
 * Represents the current navigation state and context
 */
data class NavigationState(
    val currentTab: Int = 0,
    val selectedCalculator: String? = null,
    val showModal: Boolean = false,
    val modalType: String? = null,
    val canNavigateBack: Boolean = true,
    val customBackAction: (() -> Unit)? = null
)

/**
 * Standard back navigation handler for calculator screens
 * Handles common scenarios like info dialogs, modal states, etc.
 */
@Composable
fun CalculatorBackHandler(
    showInfoDialog: Boolean = false,
    onInfoDialogDismiss: () -> Unit = {},
    showModal: Boolean = false,
    onModalDismiss: () -> Unit = {},
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        when {
            showInfoDialog -> {
                onInfoDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showModal -> {
                onModalDismiss()
                FeedbackUtil.buttonPress(context)
            }
            customBackAction != null -> {
                customBackAction()
                FeedbackUtil.buttonPress(context)
            }
            else -> {
                // Let MainScreen handle the back press
                // This will navigate back to calculator selection
                FeedbackUtil.buttonPress(context)
            }
        }
    }
}

/**
 * Back navigation handler for screens with multiple modal states
 * Handles complex navigation scenarios with multiple overlays
 */
@Composable
fun MultiModalBackHandler(
    modalStates: List<Pair<Boolean, () -> Unit>>,
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        // Find the first active modal and close it
        val activeModal = modalStates.firstOrNull { it.first }
        
        when {
            activeModal != null -> {
                activeModal.second()
                FeedbackUtil.buttonPress(context)
            }
            customBackAction != null -> {
                customBackAction()
                FeedbackUtil.buttonPress(context)
            }
            else -> {
                // Let parent handle the back press
                FeedbackUtil.buttonPress(context)
            }
        }
    }
}

/**
 * Back navigation handler for finance screens
 * Handles finance-specific navigation scenarios
 */
@Composable
fun FinanceBackHandler(
    showDeleteConfirmation: Boolean = false,
    onDeleteConfirmationDismiss: () -> Unit = {},
    showInfoDialog: Boolean = false,
    onInfoDialogDismiss: () -> Unit = {},
    showFilterDialog: Boolean = false,
    onFilterDialogDismiss: () -> Unit = {},
    showExportDialog: Boolean = false,
    onExportDialogDismiss: () -> Unit = {},
    showBudgetDialog: Boolean = false,
    onBudgetDialogDismiss: () -> Unit = {},
    showDeleteAllConfirmation: Boolean = false,
    onDeleteAllConfirmationDismiss: () -> Unit = {},
    showWelcomeDialog: Boolean = false,
    onWelcomeDialogDismiss: () -> Unit = {},
    showSearch: Boolean = false,
    onSearchDismiss: () -> Unit = {},
    currentEntry: Any? = null,
    onCurrentEntryDismiss: () -> Unit = {},
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        when {
            showDeleteConfirmation -> {
                onDeleteConfirmationDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showInfoDialog -> {
                onInfoDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showFilterDialog -> {
                onFilterDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showExportDialog -> {
                onExportDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showBudgetDialog -> {
                onBudgetDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showDeleteAllConfirmation -> {
                onDeleteAllConfirmationDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showWelcomeDialog -> {
                onWelcomeDialogDismiss()
                FeedbackUtil.buttonPress(context)
            }
            showSearch -> {
                onSearchDismiss()
                FeedbackUtil.buttonPress(context)
            }
            currentEntry != null -> {
                onCurrentEntryDismiss()
                FeedbackUtil.buttonPress(context)
            }
            customBackAction != null -> {
                customBackAction()
                FeedbackUtil.buttonPress(context)
            }
            else -> {
                // Let MainScreen handle the back press
                FeedbackUtil.buttonPress(context)
            }
        }
    }
}

/**
 * Simple back navigation handler for basic screens
 * Provides standard back button behavior with feedback
 */
@Composable
fun SimpleBackHandler(
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        if (customBackAction != null) {
            customBackAction()
        }
        FeedbackUtil.buttonPress(context)
    }
}

/**
 * Back navigation handler for screens with confirmation dialogs
 * Handles scenarios where user needs to confirm before navigating back
 */
@Composable
fun ConfirmationBackHandler(
    showConfirmation: Boolean = false,
    onConfirmationDismiss: () -> Unit = {},
    onConfirmBack: () -> Unit = {},
    customBackAction: (() -> Unit)? = null
) {
    val context = LocalContext.current

    BackHandler {
        when {
            showConfirmation -> {
                onConfirmationDismiss()
                FeedbackUtil.buttonPress(context)
            }
            customBackAction != null -> {
                customBackAction()
                FeedbackUtil.buttonPress(context)
            }
            else -> {
                onConfirmBack()
                FeedbackUtil.buttonPress(context)
            }
        }
    }
}

/**
 * Navigation utilities for common navigation patterns
 */
object NavigationUtils {
    
    /**
     * Creates a standard modal state list for MultiModalBackHandler
     */
    fun createModalStates(vararg modals: Pair<Boolean, () -> Unit>): List<Pair<Boolean, () -> Unit>> {
        return modals.toList()
    }
    
    /**
     * Checks if any modal is currently active
     */
    fun hasActiveModal(vararg modalStates: Boolean): Boolean {
        return modalStates.any { it }
    }
    
    /**
     * Creates a navigation state for complex navigation scenarios
     */
    fun createNavigationState(
        currentTab: Int = 0,
        selectedCalculator: String? = null,
        showModal: Boolean = false,
        modalType: String? = null,
        canNavigateBack: Boolean = true,
        customBackAction: (() -> Unit)? = null
    ): NavigationState {
        return NavigationState(
            currentTab = currentTab,
            selectedCalculator = selectedCalculator,
            showModal = showModal,
            modalType = modalType,
            canNavigateBack = canNavigateBack,
            customBackAction = customBackAction
        )
    }
}
