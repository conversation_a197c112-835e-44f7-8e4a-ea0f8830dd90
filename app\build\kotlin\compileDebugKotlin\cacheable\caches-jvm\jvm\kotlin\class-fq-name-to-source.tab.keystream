#com.app.wordifynumbers.MainActivity/com.app.wordifynumbers.NumberConverterViewModel6com.app.wordifynumbers.NumberConverterViewModelFactorycom.app.wordifynumbers.TestApp9com.app.wordifynumbers.accessibility.AccessibilityManagerMcom.app.wordifynumbers.accessibility.AccessibilityManager.ContentDescriptionsGcom.app.wordifynumbers.accessibility.AccessibilityManager.SemanticRolesKcom.app.wordifynumbers.accessibility.AccessibilityManager.StateDescriptions7com.app.wordifynumbers.accessibility.AccessibilityUtils1com.app.wordifynumbers.data.CalculatorPreferences;com.app.wordifynumbers.data.CalculatorPreferences.Companion=com.app.wordifynumbers.data.CalculatorPreferences.$serializer;com.app.wordifynumbers.data.CalculatorPreferencesSerializer,com.app.wordifynumbers.data.DataStoreManager<com.app.wordifynumbers.data.DataStoreManager.PreferencesKeys-com.app.wordifynumbers.data.DefaultCategories'com.app.wordifynumbers.data.FinanceNote1com.app.wordifynumbers.data.FinanceNote.Companion3com.app.wordifynumbers.data.FinanceNote.$serializer com.app.wordifynumbers.data.Task*com.app.wordifynumbers.data.Task.Companion,com.app.wordifynumbers.data.Task.$serializer(com.app.wordifynumbers.data.TaskPriority(com.app.wordifynumbers.data.NoteCategory1com.app.wordifynumbers.data.FinanceNoteRepository;com.app.wordifynumbers.data.FinanceNoteRepository.Companion(com.app.wordifynumbers.data.NumberFormat"com.app.wordifynumbers.data.Entity&com.app.wordifynumbers.data.PrimaryKeycom.app.wordifynumbers.data.Dao$com.app.wordifynumbers.data.Database*com.app.wordifynumbers.data.TypeConverters"com.app.wordifynumbers.data.Insert"com.app.wordifynumbers.data.Update"com.app.wordifynumbers.data.Delete!com.app.wordifynumbers.data.Query)com.app.wordifynumbers.data.TypeConverter.com.app.wordifynumbers.data.OnConflictStrategy(com.app.wordifynumbers.data.RoomDatabase1com.app.wordifynumbers.data.RoomDatabase.Callback com.app.wordifynumbers.data.Room+com.app.wordifynumbers.data.DatabaseBuilder1com.app.wordifynumbers.data.SupportSQLiteDatabase5com.app.wordifynumbers.data.SimpleFinanceDatabaseStub?com.app.wordifynumbers.data.SimpleFinanceDatabaseStub.Companion5com.app.wordifynumbers.data.SimpleFinanceEntryDaoStub1com.app.wordifynumbers.data.SimpleFinanceDatabase;com.app.wordifynumbers.data.SimpleFinanceDatabase.CompanionRcom.app.wordifynumbers.data.SimpleFinanceDatabase.Companion.SimpleDatabaseCallback&com.app.wordifynumbers.data.Converters.com.app.wordifynumbers.data.SimpleFinanceEntry8com.app.wordifynumbers.data.SimpleFinanceEntry.Companion%com.app.wordifynumbers.data.EntryType1com.app.wordifynumbers.data.SimpleFinanceEntryDao3com.app.wordifynumbers.data.SimpleFinanceRepository*com.app.wordifynumbers.data.TaskRepository.com.app.wordifynumbers.model.ConversionProfile8com.app.wordifynumbers.model.ConversionProfile.Companion(com.app.wordifynumbers.model.IncomeEntry2com.app.wordifynumbers.model.IncomeEntry.Companion+com.app.wordifynumbers.model.IncomeCategory2com.app.wordifynumbers.model.IncomeRecurringPeriod,com.app.wordifynumbers.model.NumberBreakdown+com.app.wordifynumbers.model.ScaleComponent.com.app.wordifynumbers.model.VisualizationUnit(<EMAIL>:com.app.wordifynumbers.performance.ComposePerformanceUtils8com.app.wordifynumbers.performance.BackgroundTaskManager4com.app.wordifynumbers.performance.ImageOptimization/com.app.wordifynumbers.performance.CacheManager-com.app.wordifynumbers.privacy.PrivacyManager:com.app.wordifynumbers.privacy.PrivacyManager.PrivacyState9com.app.wordifynumbers.privacy.PrivacyManager.UserConsent<com.app.wordifynumbers.privacy.PrivacyManager.UserDataExportAcom.app.wordifynumbers.privacy.PrivacyManager.DataRetentionPolicyCcom.app.wordifynumbers.privacy.PrivacyManager.DataCollectionPurpose3com.app.wordifynumbers.privacy.PrivacyPolicyContent2com.app.wordifynumbers.repository.IncomeRepository/com.app.wordifynumbers.security.SecurityManager=com.app.wordifynumbers.security.SecurityManager.EncryptedData=com.app.wordifynumbers.security.SecurityManager.SecureStorage(com.app.wordifynumbers.ui.NavigationItem.com.app.wordifynumbers.ui.components.AngleUnit/com.app.wordifynumbers.ui.components.ButtonType1com.app.wordifynumbers.ui.components.HistoryEntry6com.app.wordifynumbers.ui.components.CalculatorNavItem7com.app.wordifynumbers.ui.components.ScientificFunction4com.app.wordifynumbers.ui.components.NeonRippleTheme<com.app.wordifynumbers.ui.components.UICalculatorPreferencesFcom.app.wordifynumbers.ui.components.UICalculatorPreferences.CompanionHcom.app.wordifynumbers.ui.components.UICalculatorPreferences.$serializer3com.app.wordifynumbers.ui.components.CalculatorType3com.app.wordifynumbers.ui.components.NavigationItem1com.app.wordifynumbers.ui.components.NumberFormat4com.app.wordifynumbers.ui.components.ValidationState5com.app.wordifynumbers.ui.components.NumberValidation6com.app.wordifynumbers.ui.components.VisualizationType(com.app.wordifynumbers.ui.model.Currency4com.app.wordifynumbers.ui.navigation.NavigationState4com.app.wordifynumbers.ui.navigation.NavigationUtils'com.app.wordifynumbers.ui.screens.Shape/com.app.wordifynumbers.ui.screens.ShapeCategory1com.app.wordifynumbers.ui.screens.MeasurementUnit3com.app.wordifynumbers.ui.screens.CalculationResult6com.app.wordifynumbers.ui.screens.BondPaymentFrequency*com.app.wordifynumbers.ui.screens.BondType4com.app.wordifynumbers.ui.screens.CalculatorCategory,com.app.wordifynumbers.ui.screens.DateResult6com.app.wordifynumbers.ui.screens.DateDifferenceResult5com.app.wordifynumbers.ui.screens.DateOperationResult7com.app.wordifynumbers.ui.screens.FractionDecimalResult*com.app.wordifynumbers.ui.screens.Fraction0com.app.wordifynumbers.ui.screens.InvestmentMode2com.app.wordifynumbers.ui.screens.InvestmentResult6com.app.wordifynumbers.ui.screens.YearlyInvestmentData0com.app.wordifynumbers.ui.screens.PercentageMode4com.app.wordifynumbers.ui.screens.PercentageCategory2com.app.wordifynumbers.ui.screens.PercentageResult1com.app.wordifynumbers.ui.screens.CalculationStep,com.app.wordifynumbers.ui.screens.VisualData3com.app.wordifynumbers.ui.screens.PercentageSegment2com.app.wordifynumbers.ui.screens.ConversionResult3com.app.wordifynumbers.ui.screens.ConversionHistory3com.app.wordifynumbers.ui.theme.NeonBorderConstants3com.app.wordifynumbers.ui.theme.FinanceTrackerTheme;com.app.wordifynumbers.ui.theme.FinanceTrackerTheme.Spacing=com.app.wordifynumbers.ui.theme.FinanceTrackerTheme.Elevation:com.app.wordifynumbers.ui.theme.FinanceTrackerTheme.Border=com.app.wordifynumbers.ui.theme.FinanceTrackerTheme.Animation+com.app.wordifynumbers.ui.theme.NeonSpacing*com.app.wordifynumbers.ui.theme.NeonBorder-com.app.wordifynumbers.ui.theme.NeonElevation-com.app.wordifynumbers.ui.theme.NeonAnimation7com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel3com.app.wordifynumbers.ui.viewmodel.CalculatorState2com.app.wordifynumbers.ui.viewmodel.FinancialState>com.app.wordifynumbers.ui.viewmodel.FinancialCalculationResult3com.app.wordifynumbers.ui.viewmodel.YearlyBreakdown>com.app.wordifynumbers.ui.viewmodel.CurrencyConverterViewModel6com.app.wordifynumbers.ui.viewmodel.CurrencyConversion?com.app.wordifynumbers.ui.viewmodel.DateTimeCalculatorViewModel,com.app.wordifynumbers.ui.viewmodel.DateMode,com.app.wordifynumbers.ui.viewmodel.TimeMode2com.app.wordifynumbers.ui.viewmodel.CalculatorMode.com.app.wordifynumbers.ui.viewmodel.DateResult.com.app.wordifynumbers.ui.viewmodel.TimeResultAcom.app.wordifynumbers.ui.viewmodel.FinancialTaskManagerViewModel,com.app.wordifynumbers.ui.viewmodel.TaskData=com.app.wordifynumbers.ui.viewmodel.HealthCalculatorViewModel.com.app.wordifynumbers.ui.viewmodel.WeightUnit.com.app.wordifynumbers.ui.viewmodel.HeightUnit*com.app.wordifynumbers.ui.viewmodel.Gender1com.app.wordifynumbers.ui.viewmodel.ActivityLevel/com.app.wordifynumbers.ui.viewmodel.BMICategory/com.app.wordifynumbers.ui.viewmodel.WHRCategory8com.app.wordifynumbers.ui.viewmodel.HealthCalculatorMode-com.app.wordifynumbers.ui.viewmodel.BMIResult-com.app.wordifynumbers.ui.viewmodel.WHRResult-com.app.wordifynumbers.ui.viewmodel.BMRResult3com.app.wordifynumbers.ui.viewmodel.IncomeViewModel:com.app.wordifynumbers.ui.viewmodel.IncomeViewModelFactory4com.app.wordifynumbers.ui.viewmodel.IncomeSortOption1com.app.wordifynumbers.ui.viewmodel.IncomeUiState9com.app.wordifynumbers.ui.viewmodel.LargeNumbersViewModelAcom.app.wordifynumbers.ui.viewmodel.ProgrammerCalculatorViewModel.com.app.wordifynumbers.ui.viewmodel.NumberBase:com.app.wordifynumbers.ui.viewmodel.SimpleFinanceViewModelAcom.app.wordifynumbers.ui.viewmodel.SimpleFinanceViewModelFactory8com.app.wordifynumbers.ui.viewmodel.SimpleFinanceUiState4com.app.wordifynumbers.ui.viewmodel.FinancialSummary.com.app.wordifynumbers.ui.viewmodel.TimePeriodAcom.app.wordifynumbers.ui.viewmodel.StatisticsCalculatorViewModel5com.app.wordifynumbers.ui.viewmodel.VisualizationType0com.app.wordifynumbers.ui.viewmodel.HistogramBin4com.app.wordifynumbers.ui.viewmodel.StatisticsResult-com.app.wordifynumbers.util.AmortizationEntry)com.app.wordifynumbers.util.AudioFeedback#com.app.wordifynumbers.util.Complex-com.app.wordifynumbers.util.Complex.Companion,com.app.wordifynumbers.util.ComplexOperation2com.app.wordifynumbers.util.CalculationHistoryItem<com.app.wordifynumbers.util.CalculationHistoryItem.Companion>com.app.wordifynumbers.util.CalculationHistoryItem.$serializer+com.app.wordifynumbers.util.CalculationType5com.app.wordifynumbers.util.CalculationType.Companion3com.app.wordifynumbers.util.ConverterHistoryManager*com.app.wordifynumbers.util.DateCalculator/com.app.wordifynumbers.util.ExpressionEvaluator(com.app.wordifynumbers.util.FeedbackUtil2com.app.wordifynumbers.util.CompoundInterestResult+com.app.wordifynumbers.util.YearlyBreakdown,com.app.wordifynumbers.util.RetirementResult,com.app.wordifynumbers.util.YearlyProjection/com.app.wordifynumbers.util.FinancialCalculator'com.app.wordifynumbers.util.FormatUtils,com.app.wordifynumbers.util.PaymentFrequency$com.app.wordifynumbers.util.LoanType1com.app.wordifynumbers.util.LoanCalculationResult1com.app.wordifynumbers.util.LoanAmortizationEntry%com.app.wordifynumbers.util.TipResult&com.app.wordifynumbers.util.BondResult/com.app.wordifynumbers.util.PlayStoreCompliance0com.app.wordifynumbers.util.ScientificCalculator)com.app.wordifynumbers.util.TaxCalculator3com.app.wordifynumbers.util.TaxCalculator.TaxSystem4com.app.wordifynumbers.util.TaxCalculator.TaxBracket:com.app.wordifynumbers.util.TaxCalculator.CountryTaxSystem)com.app.wordifynumbers.util.TipCalculator)com.app.wordifynumbers.util.UnitConverter-com.app.wordifynumbers.utils.CulturalInsights,com.app.wordifynumbers.utils.DigitTranslator-com.app.wordifynumbers.utils.LargeNumberUtils*com.app.wordifynumbers.utils.NumberToWords"com.app.wordifynumbers.BuildConfig2com.app.wordifynumbers.ui.components.ButtonVariant,com.app.wordifynumbers.util.EnhancedCurrency4com.app.wordifynumbers.util.CurrencyConversionResult.com.app.wordifynumbers.util.ExchangeRateResult6com.app.wordifynumbers.util.ExchangeRateResult.Success4com.app.wordifynumbers.util.ExchangeRateResult.Error6com.app.wordifynumbers.util.ExchangeRateResult.Loading5com.app.wordifynumbers.util.ExchangeRateResult.Cached4com.app.wordifynumbers.util.CurrencyValidationResult:com.app.wordifynumbers.util.CurrencyValidationResult.Valid<com.app.wordifynumbers.util.CurrencyValidationResult.Invalid5com.app.wordifynumbers.util.EnhancedCurrencyConverter?com.app.wordifynumbers.util.EnhancedCurrencyConverter.Companion.com.app.wordifynumbers.util.FinancialPrecision,com.app.wordifynumbers.util.ValidationResult2com.app.wordifynumbers.util.ValidationResult.Valid4com.app.wordifynumbers.util.ValidationResult.Invalid-com.app.wordifynumbers.util.CalculationResult5com.app.wordifynumbers.util.CalculationResult.Success3com.app.wordifynumbers.util.CalculationResult.Error:com.app.wordifynumbers.util.CalculationResult.InvalidInput:com.app.wordifynumbers.util.EnhancedCompoundInterestResult3com.app.wordifynumbers.util.EnhancedYearlyBreakdown/com.app.wordifynumbers.util.CalculationMetadata4com.app.wordifynumbers.util.EnhancedRetirementResult4com.app.wordifynumbers.util.EnhancedYearlyProjection7com.app.wordifynumbers.util.EnhancedFinancialCalculator.com.app.wordifynumbers.util.EnhancedTaxBracket-com.app.wordifynumbers.util.EnhancedTaxSystem0com.app.wordifynumbers.util.TaxCalculationResult1com.app.wordifynumbers.util.TaxBracketCalculation*com.app.wordifynumbers.util.SalesTaxResult1com.app.wordifynumbers.util.EnhancedTaxCalculator                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     