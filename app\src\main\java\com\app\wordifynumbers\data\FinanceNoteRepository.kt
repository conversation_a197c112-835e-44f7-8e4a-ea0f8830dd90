package com.app.wordifynumbers.data

import android.content.Context
import androidx.datastore.core.DataStore
import androidx.datastore.preferences.core.Preferences
import androidx.datastore.preferences.core.edit
import androidx.datastore.preferences.core.stringPreferencesKey
import androidx.datastore.preferences.preferencesDataStore
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.map
import kotlinx.serialization.decodeFromString
import kotlinx.serialization.encodeToString
import kotlinx.serialization.json.Json

/**
 * Repository for managing finance notes
 */
class FinanceNoteRepository(private val context: Context) {
    
    companion object {
        private val Context.dataStore: DataStore<Preferences> by preferencesDataStore(name = "finance_notes")
        private val NOTES_KEY = stringPreferencesKey("finance_notes")
        private val json = Json { ignoreUnknownKeys = true }
    }
    
    /**
     * Get all finance notes
     */
    val notes: Flow<List<FinanceNote>> = context.dataStore.data.map { preferences ->
        val notesJson = preferences[NOTES_KEY] ?: "[]"
        try {
            json.decodeFromString<List<FinanceNote>>(notesJson)
        } catch (e: Exception) {
            emptyList()
        }
    }
    
    /**
     * Add or update a finance note
     */
    suspend fun saveNote(note: FinanceNote) {
        context.dataStore.edit { preferences ->
            val currentNotesJson = preferences[NOTES_KEY] ?: "[]"
            val currentNotes = try {
                json.decodeFromString<List<FinanceNote>>(currentNotesJson)
            } catch (e: Exception) {
                emptyList()
            }
            
            val updatedNotes = currentNotes.filter { it.id != note.id } + note.copy(
                updatedAt = System.currentTimeMillis()
            )
            
            preferences[NOTES_KEY] = json.encodeToString(updatedNotes)
        }
    }
    
    /**
     * Delete a finance note
     */
    suspend fun deleteNote(noteId: String) {
        context.dataStore.edit { preferences ->
            val currentNotesJson = preferences[NOTES_KEY] ?: "[]"
            val currentNotes = try {
                json.decodeFromString<List<FinanceNote>>(currentNotesJson)
            } catch (e: Exception) {
                emptyList()
            }
            
            val updatedNotes = currentNotes.filter { it.id != noteId }
            
            preferences[NOTES_KEY] = json.encodeToString(updatedNotes)
        }
    }
    
    /**
     * Toggle the favorite status of a note
     */
    suspend fun toggleFavorite(noteId: String) {
        context.dataStore.edit { preferences ->
            val currentNotesJson = preferences[NOTES_KEY] ?: "[]"
            val currentNotes = try {
                json.decodeFromString<List<FinanceNote>>(currentNotesJson)
            } catch (e: Exception) {
                emptyList()
            }
            
            val updatedNotes = currentNotes.map { 
                if (it.id == noteId) {
                    it.copy(
                        isFavorite = !it.isFavorite,
                        updatedAt = System.currentTimeMillis()
                    )
                } else {
                    it
                }
            }
            
            preferences[NOTES_KEY] = json.encodeToString(updatedNotes)
        }
    }
    
    /**
     * Update a task in a note
     */
    suspend fun updateTask(noteId: String, taskId: String, isCompleted: Boolean) {
        context.dataStore.edit { preferences ->
            val currentNotesJson = preferences[NOTES_KEY] ?: "[]"
            val currentNotes = try {
                json.decodeFromString<List<FinanceNote>>(currentNotesJson)
            } catch (e: Exception) {
                emptyList()
            }
            
            val updatedNotes = currentNotes.map { note -> 
                if (note.id == noteId) {
                    val updatedTasks = note.tasks.map { task ->
                        if (task.id == taskId) {
                            task.copy(isCompleted = isCompleted)
                        } else {
                            task
                        }
                    }
                    note.copy(
                        tasks = updatedTasks,
                        updatedAt = System.currentTimeMillis()
                    )
                } else {
                    note
                }
            }
            
            preferences[NOTES_KEY] = json.encodeToString(updatedNotes)
        }
    }
}
