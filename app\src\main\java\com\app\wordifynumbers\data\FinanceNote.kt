package com.app.wordifynumbers.data

import androidx.compose.ui.graphics.Color
import kotlinx.serialization.Serializable
import java.util.UUID

/**
 * Represents a finance note with tasks and checklists
 */
@Serializable
data class FinanceNote(
    val id: String = UUID.randomUUID().toString(),
    val title: String = "",
    val content: String = "",
    val tasks: List<Task> = emptyList(),
    val color: Int = 0xFF1E1E1E.toInt(), // Default dark theme color
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis(),
    val category: NoteCategory = NoteCategory.GENERAL,
    val isFavorite: Boolean = false
)

/**
 * Represents a task in a finance note
 */
@Serializable
data class Task(
    val id: String = UUID.randomUUID().toString(),
    val description: String,
    val isCompleted: Boolean = false,
    val dueDate: Long? = null,
    val priority: TaskPriority = TaskPriority.MEDIUM
)

/**
 * Represents the priority of a task
 */
enum class TaskPriority {
    LOW, MEDIUM, HIGH
}

/**
 * Represents the category of a finance note
 */
enum class NoteCategory {
    GENERAL, EXPENSE, INCOME, BUDGET, INVESTMENT, DEBT
}

/**
 * Extension function to get the display name of a note category
 */
fun NoteCategory.displayName(): String {
    return when (this) {
        NoteCategory.GENERAL -> "General"
        NoteCategory.EXPENSE -> "Expense"
        NoteCategory.INCOME -> "Income"
        NoteCategory.BUDGET -> "Budget"
        NoteCategory.INVESTMENT -> "Investment"
        NoteCategory.DEBT -> "Debt"
    }
}

/**
 * Extension function to get the color of a note category
 */
fun NoteCategory.color(): Color {
    return when (this) {
        NoteCategory.GENERAL -> Color(0xFF64B5F6) // Blue
        NoteCategory.EXPENSE -> Color(0xFFE57373) // Red
        NoteCategory.INCOME -> Color(0xFF81C784) // Green
        NoteCategory.BUDGET -> Color(0xFFFFD54F) // Yellow
        NoteCategory.INVESTMENT -> Color(0xFF9575CD) // Purple
        NoteCategory.DEBT -> Color(0xFFFF8A65) // Orange
    }
}

/**
 * Extension function to get the icon name of a note category
 */
fun NoteCategory.iconName(): String {
    return when (this) {
        NoteCategory.GENERAL -> "note"
        NoteCategory.EXPENSE -> "money_off"
        NoteCategory.INCOME -> "attach_money"
        NoteCategory.BUDGET -> "account_balance"
        NoteCategory.INVESTMENT -> "trending_up"
        NoteCategory.DEBT -> "credit_card"
    }
}
