package com.app.wordifynumbers.data

import android.content.Context
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.map
import java.util.Calendar

/**
 * Repository for managing finance entries
 */
class SimpleFinanceRepository(val context: Context) {

    private val database = SimpleFinanceDatabase.getDatabase(context)
    private val financeEntryDao = database.financeEntryDao()

    /**
     * Get all entries
     */
    val allEntries: Flow<List<SimpleFinanceEntry>> = financeEntryDao.getAllEntries()

    /**
     * Get total income
     */
    val totalIncome: Flow<Double> = financeEntryDao.getTotalIncome().map { it ?: 0.0 }

    /**
     * Get total expense
     */
    val totalExpense: Flow<Double> = financeEntryDao.getTotalExpense().map { it ?: 0.0 }

    /**
     * Get balance (income - expense)
     */
    val balance: Flow<Double> = totalIncome.combine(totalExpense) { income, expense ->
        income - expense
    }

    /**
     * Insert a new entry
     */
    suspend fun insert(entry: SimpleFinanceEntry) {
        financeEntryDao.insert(entry)
    }

    /**
     * Update an existing entry
     */
    suspend fun update(entry: SimpleFinanceEntry) {
        financeEntryDao.update(entry)
    }

    /**
     * Delete an entry
     */
    suspend fun delete(entry: SimpleFinanceEntry) {
        financeEntryDao.delete(entry)
    }

    /**
     * Delete an entry by ID
     */
    suspend fun deleteById(id: String) {
        financeEntryDao.deleteById(id)
    }

    /**
     * Delete all entries
     */
    suspend fun deleteAllEntries() {
        financeEntryDao.deleteAllEntries()
    }

    /**
     * Get entries by type
     */
    fun getEntriesByType(type: EntryType): Flow<List<SimpleFinanceEntry>> {
        return financeEntryDao.getEntriesByType(type)
    }

    /**
     * Get entries by category
     */
    fun getEntriesByCategory(category: String): Flow<List<SimpleFinanceEntry>> {
        return financeEntryDao.getEntriesByCategory(category)
    }

    /**
     * Search entries
     */
    fun searchEntries(query: String): Flow<List<SimpleFinanceEntry>> {
        return financeEntryDao.searchEntries(query)
    }

    /**
     * Get entries for this week
     */
    fun getEntriesForThisWeek(): Flow<List<SimpleFinanceEntry>> {
        val calendar = Calendar.getInstance()

        // Set to start of current week (Sunday)
        calendar.set(Calendar.DAY_OF_WEEK, Calendar.SUNDAY)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfWeek = calendar.timeInMillis

        // Set to end of current week (Saturday)
        calendar.add(Calendar.DAY_OF_WEEK, 6)
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        val endOfWeek = calendar.timeInMillis

        return financeEntryDao.getEntriesInDateRange(startOfWeek, endOfWeek)
    }

    /**
     * Get entries for this month
     */
    fun getEntriesForThisMonth(): Flow<List<SimpleFinanceEntry>> {
        val calendar = Calendar.getInstance()

        // Set to start of current month
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis

        // Set to end of current month
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        val endOfMonth = calendar.timeInMillis

        return financeEntryDao.getEntriesInDateRange(startOfMonth, endOfMonth)
    }

    /**
     * Get total income for this month
     */
    fun getTotalIncomeForThisMonth(): Flow<Double> {
        val calendar = Calendar.getInstance()

        // Set to start of current month
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis

        // Set to end of current month
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        val endOfMonth = calendar.timeInMillis

        return financeEntryDao.getTotalIncomeInDateRange(startOfMonth, endOfMonth).map { it ?: 0.0 }
    }

    /**
     * Get total expense for this month
     */
    fun getTotalExpenseForThisMonth(): Flow<Double> {
        val calendar = Calendar.getInstance()

        // Set to start of current month
        calendar.set(Calendar.DAY_OF_MONTH, 1)
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val startOfMonth = calendar.timeInMillis

        // Set to end of current month
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH))
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        val endOfMonth = calendar.timeInMillis

        return financeEntryDao.getTotalExpenseInDateRange(startOfMonth, endOfMonth).map { it ?: 0.0 }
    }
}
