package com.app.wordifynumbers.model

import android.content.SharedPreferences
import org.json.JSONArray
import org.json.JSONObject
import java.util.*

/**
 * Data class representing an income entry
 */
data class IncomeEntry(
    val id: String = UUID.randomUUID().toString(),
    val title: String,
    val description: String = "",
    val amount: Double,
    val currency: String = "USD",
    val category: IncomeCategory,
    val date: Long = System.currentTimeMillis(),
    val isRecurring: Boolean = false,
    val recurringPeriod: IncomeRecurringPeriod? = null,
    val tags: List<String> = emptyList(),
    val notes: String = ""
) {
    companion object {
        private const val INCOME_ENTRIES_KEY = "income_entries"

        /**
         * Save a list of income entries to SharedPreferences
         */
        fun saveEntries(sharedPreferences: SharedPreferences, entries: List<IncomeEntry>) {
            val jsonArray = JSONArray()
            entries.forEach { entry ->
                val jsonObject = JSONObject().apply {
                    put("id", entry.id)
                    put("title", entry.title)
                    put("description", entry.description)
                    put("amount", entry.amount)
                    put("currency", entry.currency)
                    put("category", entry.category.name)
                    put("date", entry.date)
                    put("isRecurring", entry.isRecurring)

                    // Handle optional fields
                    entry.recurringPeriod?.let { put("recurringPeriod", it.name) }

                    // Handle tags
                    val tagsArray = JSONArray()
                    entry.tags.forEach { tag -> tagsArray.put(tag) }
                    put("tags", tagsArray)

                    put("notes", entry.notes)
                }
                jsonArray.put(jsonObject)
            }
            sharedPreferences.edit().putString(INCOME_ENTRIES_KEY, jsonArray.toString()).apply()
        }

        /**
         * Load income entries from SharedPreferences
         */
        fun loadEntries(sharedPreferences: SharedPreferences): List<IncomeEntry> {
            val entriesJson = sharedPreferences.getString(INCOME_ENTRIES_KEY, null) ?: return emptyList()
            return try {
                val jsonArray = JSONArray(entriesJson)
                val entries = mutableListOf<IncomeEntry>()

                for (i in 0 until jsonArray.length()) {
                    val jsonObject = jsonArray.getJSONObject(i)

                    // Parse tags
                    val tags = mutableListOf<String>()
                    val tagsArray = jsonObject.optJSONArray("tags")
                    if (tagsArray != null) {
                        for (j in 0 until tagsArray.length()) {
                            tags.add(tagsArray.getString(j))
                        }
                    }

                    // Create entry
                    val entry = IncomeEntry(
                        id = jsonObject.getString("id"),
                        title = jsonObject.getString("title"),
                        description = jsonObject.optString("description", ""),
                        amount = jsonObject.getDouble("amount"),
                        currency = jsonObject.optString("currency", "USD"),
                        category = IncomeCategory.valueOf(jsonObject.getString("category")),
                        date = jsonObject.getLong("date"),
                        isRecurring = jsonObject.getBoolean("isRecurring"),
                        recurringPeriod = if (jsonObject.has("recurringPeriod"))
                            IncomeRecurringPeriod.valueOf(jsonObject.getString("recurringPeriod")) else null,
                        tags = tags,
                        notes = jsonObject.optString("notes", "")
                    )
                    entries.add(entry)
                }
                entries
            } catch (e: Exception) {
                emptyList()
            }
        }
    }
}

/**
 * Enum representing income categories
 */
enum class IncomeCategory(val displayName: String) {
    SALARY("Salary"),
    BUSINESS("Business"),
    INVESTMENT("Investment"),
    RENTAL("Rental"),
    DIVIDEND("Dividend"),
    INTEREST("Interest"),
    GIFT("Gift"),
    REFUND("Refund"),
    BONUS("Bonus"),
    OTHER("Other")
}

/**
 * Enum representing recurring periods for income
 */
enum class IncomeRecurringPeriod(val displayName: String) {
    DAILY("Daily"),
    WEEKLY("Weekly"),
    BIWEEKLY("Bi-weekly"),
    MONTHLY("Monthly"),
    QUARTERLY("Quarterly"),
    BIANNUALLY("Bi-annually"),
    ANNUALLY("Annually")
}
