/ Header Record For PersistentHashMapValueStorage$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity3 2kotlinx.serialization.internal.GeneratedSerializer# "androidx.datastore.core.Serializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation2 1com.app.wordifynumbers.data.SimpleFinanceDatabase2 1com.app.wordifynumbers.data.SimpleFinanceEntryDao) (com.app.wordifynumbers.data.RoomDatabase2 1com.app.wordifynumbers.data.RoomDatabase.Callback kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum, +androidx.lifecycle.DefaultLifecycleObserver kotlin.Enum kotlin.Enum kotlin.Enum- ,androidx.compose.material.ripple.RippleTheme3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum- ,com.app.wordifynumbers.ui.screens.DateResult- ,com.app.wordifynumbers.ui.screens.DateResult kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.activity.ComponentActivity androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory$ #androidx.activity.ComponentActivity3 2kotlinx.serialization.internal.GeneratedSerializer# "androidx.datastore.core.Serializer3 2kotlinx.serialization.internal.GeneratedSerializer3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation kotlin.Annotation2 1com.app.wordifynumbers.data.SimpleFinanceDatabase2 1com.app.wordifynumbers.data.SimpleFinanceEntryDao) (com.app.wordifynumbers.data.RoomDatabase2 1com.app.wordifynumbers.data.RoomDatabase.Callback kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum, +androidx.lifecycle.DefaultLifecycleObserver kotlin.Enum kotlin.Enum kotlin.Enum- ,androidx.compose.material.ripple.RippleTheme3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum- ,com.app.wordifynumbers.ui.screens.DateResult- ,com.app.wordifynumbers.ui.screens.DateResult kotlin.Enum kotlin.Enum kotlin.Enum$ #androidx.lifecycle.AndroidViewModel androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum androidx.lifecycle.ViewModel androidx.lifecycle.ViewModel kotlin.Enum androidx.lifecycle.ViewModel- ,androidx.lifecycle.ViewModelProvider.Factory kotlin.Enum androidx.lifecycle.ViewModel kotlin.Enum kotlin.Enum3 2kotlinx.serialization.internal.GeneratedSerializer kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum kotlin.Enum/ .com.app.wordifynumbers.util.ExchangeRateResult/ .com.app.wordifynumbers.util.ExchangeRateResult/ .com.app.wordifynumbers.util.ExchangeRateResult/ .com.app.wordifynumbers.util.ExchangeRateResult5 4com.app.wordifynumbers.util.CurrencyValidationResult5 4com.app.wordifynumbers.util.CurrencyValidationResult- ,com.app.wordifynumbers.util.ValidationResult- ,com.app.wordifynumbers.util.ValidationResult. -com.app.wordifynumbers.util.CalculationResult. -com.app.wordifynumbers.util.CalculationResult. -com.app.wordifynumbers.util.CalculationResult