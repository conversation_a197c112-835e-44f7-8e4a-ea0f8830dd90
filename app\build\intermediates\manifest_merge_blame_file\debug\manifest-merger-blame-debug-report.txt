1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.app.wordifynumbers.debug"
4    android:versionCode="1"
5    android:versionName="1.0-debug" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!-- Essential permissions -->
11    <uses-permission android:name="android.permission.VIBRATE" />
11-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:5:5-65
11-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:5:22-63
12
13    <!-- Optional permissions for enhanced features -->
14    <uses-permission
14-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:8:5-9:51
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:8:22-78
16        android:maxSdkVersion="28" />
16-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:9:22-48
17    <uses-permission
17-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:10:5-11:51
18        android:name="android.permission.READ_EXTERNAL_STORAGE"
18-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:10:22-77
19        android:maxSdkVersion="32" />
19-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:11:22-48
20
21    <!-- Biometric authentication for secure features -->
22    <uses-permission android:name="android.permission.USE_BIOMETRIC" />
22-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:14:5-72
22-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:14:22-69
23    <uses-permission android:name="android.permission.USE_FINGERPRINT" />
23-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:15:5-74
23-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:15:22-71
24
25    <!-- Network state for connectivity checks -->
26    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
26-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:18:5-79
26-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:18:22-76
27
28    <permission
28-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
29        android:name="com.app.wordifynumbers.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
29-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
30        android:protectionLevel="signature" />
30-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
31
32    <uses-permission android:name="com.app.wordifynumbers.debug.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
32-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
32-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
33
34    <application
34-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:20:5-44:19
35        android:allowBackup="true"
35-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:21:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.12.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\051ef852574ec70fd9adbbe1ad34e80f\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:22:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="true"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:23:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:24:9-43
42        android:label="@string/app_name"
42-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:25:9-41
43        android:networkSecurityConfig="@xml/network_security_config"
43-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:29:9-69
44        android:preserveLegacyExternalStorage="false"
44-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:31:9-54
45        android:requestLegacyExternalStorage="false"
45-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:30:9-53
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:26:9-54
47        android:supportsRtl="true"
47-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:27:9-35
48        android:theme="@style/Theme.WordifyNumbers" >
48-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:28:9-52
49        <activity
49-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:33:9-43:20
50            android:name="com.app.wordifynumbers.MainActivity"
50-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:34:13-41
51            android:exported="true"
51-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:35:13-36
52            android:label="@string/app_name"
52-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:36:13-45
53            android:theme="@style/Theme.WordifyNumbers" >
53-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:37:13-56
54            <intent-filter>
54-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:38:13-42:29
55                <action android:name="android.intent.action.MAIN" />
55-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:39:17-69
55-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:39:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:41:17-77
57-->D:\Wordify Numbers\app\src\main\AndroidManifest.xml:41:27-74
58            </intent-filter>
59        </activity>
60        <activity
60-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
61            android:name="androidx.compose.ui.tooling.PreviewActivity"
61-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
62            android:exported="true" />
62-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\d5c0c7c2641eecb87689e45c9eb3a83c\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
63        <activity
63-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
64            android:name="androidx.activity.ComponentActivity"
64-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
65            android:exported="true" />
65-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\ff02f6e10a46c673194821c2c5cfeff6\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
66
67        <provider
67-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.app.wordifynumbers.debug.androidx-startup"
69-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.4.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\9b3aa2a5659cf7e7190f198c6550f650\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\a72a25de53dcd6e3c38d92fb8a587888\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <service
82-->[androidx.room:room-runtime:2.5.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\3a69605ba40e1c838d2a0713f7574c15\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
83            android:name="androidx.room.MultiInstanceInvalidationService"
83-->[androidx.room:room-runtime:2.5.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\3a69605ba40e1c838d2a0713f7574c15\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
84            android:directBootAware="true"
84-->[androidx.room:room-runtime:2.5.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\3a69605ba40e1c838d2a0713f7574c15\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
85            android:exported="false" />
85-->[androidx.room:room-runtime:2.5.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\3a69605ba40e1c838d2a0713f7574c15\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
86
87        <receiver
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
88            android:name="androidx.profileinstaller.ProfileInstallReceiver"
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
89            android:directBootAware="false"
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
90            android:enabled="true"
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
91            android:exported="true"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
92            android:permission="android.permission.DUMP" >
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
93            <intent-filter>
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
94                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
95            </intent-filter>
96            <intent-filter>
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
97                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
98            </intent-filter>
99            <intent-filter>
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
100                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
101            </intent-filter>
102            <intent-filter>
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
103                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
103-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Talash Ai\project-gradle-home\caches\8.10\transforms\c08f59e4400730178c417c7d507d423b\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
104            </intent-filter>
105        </receiver>
106    </application>
107
108</manifest>
