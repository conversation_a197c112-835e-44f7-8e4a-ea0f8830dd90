@file:OptIn(kotlinx.serialization.InternalSerializationApi::class)

package com.app.wordifynumbers.ui.viewmodel

import android.app.Application
import androidx.lifecycle.AndroidViewModel
import androidx.lifecycle.viewModelScope
import com.app.wordifynumbers.data.DataStoreManager
import com.app.wordifynumbers.ui.components.NumberFormat
import com.app.wordifynumbers.ui.components.UICalculatorPreferences
import com.app.wordifynumbers.util.CalculationHistoryItem
import com.app.wordifynumbers.util.CalculationType
import com.app.wordifynumbers.util.ExpressionEvaluator
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import kotlin.math.PI
import kotlin.math.abs
import kotlin.math.floor
import kotlin.math.log10
import kotlin.math.pow
import kotlin.math.sin
import kotlin.math.sqrt

class CalculatorViewModel(application: Application) : AndroidViewModel(application) {
    // Add preferences state
    // Updated to use UICalculatorPreferences
    private val _preferences: MutableStateFlow<UICalculatorPreferences> = MutableStateFlow(UICalculatorPreferences())
    val preferences: StateFlow<UICalculatorPreferences> = _preferences

    // Preferences are initialized in MutableStateFlow with default UICalculatorPreferences()
    // If loading from persistent storage is needed, do it in init block.

    // Add history persistence
    private val _history: MutableStateFlow<List<CalculationHistoryItem>> = MutableStateFlow(emptyList())
    val history: StateFlow<List<CalculationHistoryItem>> = _history

    // UI state for calculator display/value
    private val _state: MutableStateFlow<CalculatorState> = MutableStateFlow(CalculatorState())
    val state: StateFlow<CalculatorState> = _state

    // Financial calculator state
    private val _financialState = MutableStateFlow(FinancialState())
    val financialState: StateFlow<FinancialState> = _financialState

    // DataStore manager for persistence
    private val dataStoreManager = DataStoreManager(application)

    init {
        // Restore state from DataStore
        viewModelScope.launch {
            dataStoreManager.calculatorPreferencesFlow.collect { prefs ->
                _preferences.value = UICalculatorPreferences(prefs)
            }
        }
        viewModelScope.launch {
            dataStoreManager.calculationHistoryFlow.collect { historyItems ->
                _history.value = historyItems
            }
        }
    }

    fun updatePreferences(newPreferences: UICalculatorPreferences) {
        _preferences.value = newPreferences
        // Save preferences to DataStore
        viewModelScope.launch {
            savePreferences(newPreferences)
        }
    }

    fun clearHistory() {
        _history.value = emptyList()
        viewModelScope.launch {
            clearSavedHistory()
        }
    }

    fun addToHistory(item: CalculationHistoryItem) {
        _history.value = (_history.value + item).takeLast(100) // Keep last 100 items
        viewModelScope.launch {
            saveHistory(_history.value)
        }
    }

    private suspend fun saveHistory(history: List<CalculationHistoryItem>) {
        dataStoreManager.saveCalculationHistory(history)
    }

    private suspend fun clearSavedHistory() {
        dataStoreManager.clearCalculationHistory()
    }

    // Save updated preferences to DataStore
    private suspend fun savePreferences(newPreferences: UICalculatorPreferences) {
        dataStoreManager.saveCalculatorPreferences(newPreferences.toDataPreferences())
    }

    private fun validateExpression(expression: String): Boolean {
        // Use the improved validation from ExpressionEvaluator
        return ExpressionEvaluator.validateExpression(expression)
    }

    private fun evaluateExpression(expression: String): Double {
        // Use the improved ExpressionEvaluator for all calculations
        return ExpressionEvaluator.evaluate(expression)
    }

    /**
     * Handles user input to the calculator
     * Validates and formats input according to locale settings
     * @param expression The character or operation to add to the current input
     */
    fun onInput(expression: String) {
        if (expression.isEmpty() || validateExpression(expression)) {
            _state.update { it.copy(display = expression, isError = false) }
        }
    }

    /**
     * Evaluates the current expression and updates the result
     * Handles errors and formats the result according to user preferences
     */
    fun onEquals() {
        val expression = state.value.display
        try {
            val result = evaluateExpression(expression)

            // Check for division by zero and other arithmetic errors
            if (result.isInfinite() || result.isNaN()) {
                _state.update { it.copy(
                    isError = true,
                    result = if (result.isInfinite()) "Division by zero" else "Invalid calculation"
                )}
                return
            }

            val formattedResult = formatResult(result)

            // Create history item
            val historyItem = CalculationHistoryItem(
                type = CalculationType.BASIC_CALCULATION,
                input = expression,
                result = formattedResult,
                timestamp = System.currentTimeMillis()
            )

            _state.update {
                it.copy(
                    result = formattedResult,
                    isError = false,
                    history = it.history + historyItem
                )
            }

            // Add to persistent history
            addToHistory(historyItem)
        } catch (e: Exception) {
            _state.update { it.copy(
                isError = true,
                result = when (e) {
                    is ArithmeticException -> "Math error: ${e.message}"
                    is IllegalArgumentException -> e.message ?: "Invalid expression"
                    else -> "Error: ${e.message}"
                }
            )}
        }
    }

    // Handlers for calculator pad
    fun onNumber(number: String) {
        onInput(state.value.display + number)
    }

    fun onOperator(op: String) {
        // Check if the last character is already an operator
        val currentDisplay = state.value.display

        // Don't add operator if display is empty, unless it's a minus sign (negative number)
        if (currentDisplay.isEmpty() && op != "-") {
            return
        }

        // Check if the last character is an operator
        val lastChar = if (currentDisplay.isNotEmpty()) currentDisplay.last().toString() else ""
        val operators = listOf("+", "-", "*", "/", "%")

        val newDisplay = when {
            lastChar in operators && op != "-" -> {
                // Replace the last operator with the new one
                currentDisplay.dropLast(1) + op
            }
            lastChar in operators && op == "-" && currentDisplay.length > 1 &&
                    currentDisplay[currentDisplay.length - 2].toString() in operators -> {
                // Don't allow triple operators like "+-+"
                currentDisplay
            }
            lastChar in operators && op == "-" -> {
                // Allow minus after another operator (for negative numbers)
                currentDisplay + op
            }
            else -> {
                // Normal case - add the operator
                currentDisplay + op
            }
        }

        // Update the display with the new expression
        _state.update { it.copy(display = newDisplay) }

        // Calculate the result as the user types
        try {
            if (newDisplay.isNotEmpty() &&
                !newDisplay.endsWith("+") &&
                !newDisplay.endsWith("-") &&
                !newDisplay.endsWith("*") &&
                !newDisplay.endsWith("/") &&
                !newDisplay.endsWith("%")) {

                val result = ExpressionEvaluator.evaluateSafe(newDisplay)
                if (result != null) {
                    _state.update { it.copy(result = formatResult(result)) }
                }
            }
        } catch (e: Exception) {
            // Ignore calculation errors during typing
        }
    }

    fun onDelete() {
        state.value.display.takeIf { it.isNotEmpty() }?.let {
            onInput(it.dropLast(1))
        }
    }

    fun onClear() {
        onInput("")
    }

    fun onScientificFunction(function: String) {
        // Handle different types of scientific functions
        val currentDisplay = state.value.display

        when {
            // Handle special functions that need to be processed differently
            function == "^2" -> {
                // Square the last number or expression
                if (currentDisplay.isNotEmpty()) {
                    // If the last part is a number or a closed parenthesis expression
                    if (currentDisplay.last().isDigit() || currentDisplay.last() == ')') {
                        _state.update { it.copy(display = "$currentDisplay^2") }
                    }
                }
            }

            function == "^" -> {
                // Power function
                if (currentDisplay.isNotEmpty()) {
                    if (currentDisplay.last().isDigit() || currentDisplay.last() == ')') {
                        _state.update { it.copy(display = "$currentDisplay^") }
                    }
                }
            }

            function == "π" || function == "e" -> {
                // Constants - add directly or with multiplication if needed
                if (currentDisplay.isNotEmpty() &&
                    (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                    // Add multiplication operator before constant
                    _state.update { it.copy(display = "$currentDisplay*$function") }
                } else {
                    _state.update { it.copy(display = "$currentDisplay$function") }
                }
            }

            function.endsWith("(") -> {
                // Functions that need arguments (sin, cos, log, etc.)
                if (currentDisplay.isNotEmpty() &&
                    (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                    // Add multiplication operator before function
                    _state.update { it.copy(display = "$currentDisplay*$function") }
                } else {
                    _state.update { it.copy(display = "$currentDisplay$function") }
                }
            }

            // Parentheses handling
            function == "(" || function == ")" -> {
                _state.update { it.copy(display = "$currentDisplay$function") }
            }

            // Trigonometric functions
            function == "sin" || function == "cos" || function == "tan" ||
            function == "asin" || function == "acos" || function == "atan" -> {
                // Add parenthesis for function call
                if (currentDisplay.isNotEmpty() &&
                    (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                    // Add multiplication operator before function
                    _state.update { it.copy(display = "$currentDisplay*$function(") }
                } else {
                    _state.update { it.copy(display = "$currentDisplay$function(") }
                }
            }

            // Logarithmic and exponential functions
            function == "log" || function == "ln" || function == "sqrt" ||
            function == "abs" || function == "10^" -> {
                if (function == "10^") {
                    // Special case for 10^x
                    if (currentDisplay.isNotEmpty() &&
                        (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                        // Add multiplication operator before function
                        _state.update { it.copy(display = "$currentDisplay*10^") }
                    } else {
                        _state.update { it.copy(display = "${currentDisplay}10^") }
                    }
                } else {
                    // Regular function with parenthesis
                    if (currentDisplay.isNotEmpty() &&
                        (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                        // Add multiplication operator before function
                        _state.update { it.copy(display = "$currentDisplay*$function(") }
                    } else {
                        _state.update { it.copy(display = "$currentDisplay$function(") }
                    }
                }
            }

            // Memory functions
            function == "MC" -> {
                // Memory Clear
                _preferences.update { it.copy(memoryValue = 0.0) }
            }

            function == "MR" -> {
                // Memory Recall
                val memoryValue = _preferences.value.memoryValue
                if (memoryValue != 0.0) {
                    if (currentDisplay.isNotEmpty() &&
                        (currentDisplay.last().isDigit() || currentDisplay.last() == ')')) {
                        // Add multiplication operator before memory value
                        _state.update { it.copy(display = "$currentDisplay*$memoryValue") }
                    } else {
                        _state.update { it.copy(display = "$currentDisplay$memoryValue") }
                    }
                }
            }

            function == "M+" -> {
                // Memory Add
                try {
                    val result = evaluateSafe(currentDisplay)
                    if (result != null) {
                        _preferences.update { it.copy(memoryValue = it.memoryValue + result) }
                    }
                } catch (e: Exception) {
                    // Ignore errors
                }
            }

            function == "M-" -> {
                // Memory Subtract
                try {
                    val result = evaluateSafe(currentDisplay)
                    if (result != null) {
                        _preferences.update { it.copy(memoryValue = it.memoryValue - result) }
                    }
                } catch (e: Exception) {
                    // Ignore errors
                }
            }

            function == "MS" -> {
                // Memory Store
                try {
                    val result = evaluateSafe(currentDisplay)
                    if (result != null) {
                        _preferences.update { it.copy(memoryValue = result) }
                    }
                } catch (e: Exception) {
                    // Ignore errors
                }
            }

            // Default case for other functions
            else -> {
                _state.update { it.copy(display = "$currentDisplay$function") }
            }
        }

        // Calculate the result as the user types (for immediate feedback)
        try {
            if (currentDisplay.isNotEmpty() &&
                !currentDisplay.endsWith("+") &&
                !currentDisplay.endsWith("-") &&
                !currentDisplay.endsWith("*") &&
                !currentDisplay.endsWith("/") &&
                !currentDisplay.endsWith("%") &&
                !currentDisplay.endsWith("(")) {

                val result = ExpressionEvaluator.evaluateSafe(currentDisplay)
                if (result != null) {
                    _state.update { it.copy(result = formatResult(result)) }
                }
            }
        } catch (e: Exception) {
            // Ignore calculation errors during typing
        }
    }

    private fun evaluateSafe(expression: String): Double? {
        return try {
            if (expression.isNotEmpty()) {
                ExpressionEvaluator.evaluate(expression)
            } else {
                null
            }
        } catch (e: Exception) {
            null
        }
    }

    /**
     * Formats a numeric result according to user preferences and locale settings
     * @param value The numeric result to format
     * @return Formatted result string
     */
    private fun formatResult(value: Double): String {
        // Handle special cases
        if (value.isInfinite() || value.isNaN()) {
            return if (value.isInfinite()) {
                if (value > 0) "∞" else "-∞"
            } else {
                "Error"
            }
        }

        // Format according to preferences
        val formatted = when (preferences.value.numberFormat) {
            NumberFormat.DECIMAL -> "%.${preferences.value.decimalPlaces}f".format(value)
            NumberFormat.SCIENTIFIC -> "%.${preferences.value.decimalPlaces}e".format(value)
            NumberFormat.ENGINEERING -> {
                val exp = floor(log10(abs(value))).toInt()
                val engExp = exp - (exp % 3)
                val coefficient = value / 10.0.pow(engExp)
                "%.${preferences.value.decimalPlaces}f×10^%d".format(coefficient, engExp)
            }
        }

        // Apply thousands separator if enabled
        return if (preferences.value.useThousandsSeparator) {
            // Format with locale-specific thousands separator
            val parts = formatted.split('.')
            val integerPart = parts[0].replace(Regex("\\B(?=(\\d{3})+(?!\\d))"), ",")
            if (parts.size > 1) "$integerPart.${parts[1]}" else integerPart
        } else {
            formatted
        }
    }
}

data class CalculatorState(
    val display: String = "",
    val operand: Double? = null,
    val operator: String? = null,
    val history: List<CalculationHistoryItem> = emptyList(),
    val result: String = "",
    val isError: Boolean = false
) {
    fun clear() = copy(
        display = "",
        operand = null,
        operator = null,
        result = "",
        isError = false
    )
}

data class FinancialState(
    val principal: String = "",
    val rate: String = "",
    val time: String = "",
    val frequency: String = "12",
    val additionalContribution: String = "",
    val calculationResult: FinancialCalculationResult? = null,
    val showAmortization: Boolean = false
)

data class FinancialCalculationResult(
    val totalAmount: Double = 0.0,
    val totalInterest: Double = 0.0,
    val effectiveRate: Double = 0.0,
    val yearlyBreakdown: List<YearlyBreakdown> = emptyList()
)

data class YearlyBreakdown(
    val year: Int = 0,
    val principal: Double = 0.0,
    val interest: Double = 0.0,
    val balance: Double = 0.0
)
