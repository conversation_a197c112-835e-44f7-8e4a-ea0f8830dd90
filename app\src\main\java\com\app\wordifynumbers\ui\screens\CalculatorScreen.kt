package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.*
import androidx.compose.animation.expandVertically
import androidx.compose.animation.fadeIn
import androidx.compose.animation.fadeOut
import androidx.compose.animation.shrinkVertically
import androidx.compose.foundation.BorderStroke
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.CalculatorViewModel
import com.app.wordifynumbers.util.FeedbackUtil

// Calculator categories
enum class CalculatorCategory(val title: String, val icon: ImageVector) {
    MATH("Math", Icons.Default.Calculate),
    SPECIAL("Special", Icons.Default.Science)
}

private val calculators = listOf(
    // Math Calculators
    CalculatorType(
        id = "basic",
        title = "Basic",
        icon = Icons.Default.Calculate,
        description = "Standard arithmetic operations",
        category = CalculatorCategory.MATH,
        accentColor = NeonBlue
    ),
    CalculatorType(
        id = "scientific",
        title = "Scientific",
        icon = Icons.Default.Science,
        description = "Advanced mathematical functions",
        category = CalculatorCategory.MATH,
        accentColor = NeonCyan
    ),
    CalculatorType(
        id = "percentage",
        title = "Percentage Calculator",
        icon = Icons.Default.Percent,
        description = "Calculate percentages, increases, and decreases",
        category = CalculatorCategory.MATH,
        accentColor = NeonCyan
    ),
    CalculatorType(
        id = "complex",
        title = "Complex Numbers",
        icon = Icons.Default.Architecture,
        description = "Complex number operations",
        category = CalculatorCategory.MATH,
        accentColor = NeonPurple
    ),
    CalculatorType(
        id = "areaVolume",
        title = "Area/Volume",
        icon = Icons.Default.Architecture,
        description = "Calculate area and volume of shapes",
        category = CalculatorCategory.MATH,
        accentColor = NeonBlue
    ),

    // Special Calculators
    CalculatorType(
        id = "statistics",
        title = "Statistics",
        icon = Icons.Default.Timeline,
        description = "Statistical analysis with international standards",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "bmi",
        title = "Health",
        icon = Icons.Default.MonitorHeart,
        description = "BMI, WHR & BMR with international standards",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "programmer",
        title = "Programmer",
        icon = Icons.Default.Code,
        description = "Binary, hex & octal conversions",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "age",
        title = "Age Calculator",
        icon = Icons.Default.Info,
        description = "Calculate age from date of birth",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonPink
    ),
    CalculatorType(
        id = "date",
        title = "Date & Time",
        icon = Icons.Default.CalendarToday,
        description = "Date and time calculations",
        category = CalculatorCategory.SPECIAL,
        accentColor = NeonOrange
    )
)

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CalculatorScreen(
    modifier: Modifier = Modifier,
    viewModel: CalculatorViewModel,
    onNavigateToCalculator: (String) -> Unit,
    onNavigateToWords: () -> Unit = {},
    onNavigateToLargeNumbers: () -> Unit = {}
) {
    var selectedCategory by remember { mutableStateOf(CalculatorCategory.MATH) }
    var selectedCalculator by remember { mutableStateOf<CalculatorType?>(calculators.firstOrNull()) }
    val context = LocalContext.current
    var recentlyUsedCalculators by remember { mutableStateOf<List<CalculatorType>>(emptyList()) }

    // Animated background with subtle pulse effect
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundPulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                Brush.radialGradient(
                    colors = listOf(
                        NeonDeepBlue,
                        NeonBackground
                    ),
                    center = Offset(0.5f, 0.5f),
                    radius = 1200f * pulseScale
                )
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(top = 16.dp, bottom = 8.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Top Bar with title and search
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Calculators",
                    style = MaterialTheme.typography.headlineMedium.copy(
                        fontWeight = FontWeight.Bold,
                        color = NeonGlow,
                        letterSpacing = 0.5.sp
                    )
                )

                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Words navigation
                    IconButton(
                        onClick = {
                            onNavigateToWords()
                            FeedbackUtil.buttonPress(context)
                        },
                        modifier = Modifier
                            .size(40.dp)
                            .background(NeonCard.copy(alpha = 0.7f), CircleShape)
                    ) {
                        Icon(
                            Icons.Default.FormatListNumbered,
                            contentDescription = "Go to Words",
                            tint = NeonGlow
                        )
                    }


                    // Large Numbers navigation
                    IconButton(
                        onClick = {
                            onNavigateToLargeNumbers()
                            FeedbackUtil.buttonPress(context)
                        },
                        modifier = Modifier
                            .size(40.dp)
                            .background(NeonCard.copy(alpha = 0.7f), CircleShape)
                    ) {
                        Icon(
                            Icons.Default.School,
                            contentDescription = "Go to Large Numbers",
                            tint = NeonGlow
                        )
                    }
                }
            }

            // Category tabs
            ScrollableTabRow(
                selectedTabIndex = selectedCategory.ordinal,
                edgePadding = 16.dp,
                containerColor = Color.Transparent,
                contentColor = NeonGlow,
                indicator = { tabPositions ->
                    Box(
                        Modifier
                            .fillMaxWidth()
                            .wrapContentSize(Alignment.BottomStart)
                            .offset(x = tabPositions[selectedCategory.ordinal].left)
                            .width(tabPositions[selectedCategory.ordinal].width)
                            .height(3.dp)
                            .padding(horizontal = 16.dp)
                            .background(
                                brush = Brush.horizontalGradient(
                                    colors = listOf(
                                        Color.Transparent,
                                        when (selectedCategory) {
                                            CalculatorCategory.MATH -> NeonBlue
                                            CalculatorCategory.SPECIAL -> NeonPink
                                        },
                                        Color.Transparent
                                    )
                                ),
                                shape = RoundedCornerShape(topStart = 2.dp, topEnd = 2.dp)
                            )
                    )
                },
                divider = {}
            ) {
                CalculatorCategory.values().forEach { category ->
                    Tab(
                        selected = selectedCategory == category,
                        onClick = {
                            selectedCategory = category
                            // Select first calculator in this category
                            selectedCalculator = calculators.firstOrNull { it.category == category }
                        },
                        text = {
                            Text(
                                text = category.title,
                                style = MaterialTheme.typography.titleMedium.copy(
                                    fontWeight = if (selectedCategory == category) FontWeight.Bold else FontWeight.Normal
                                )
                            )
                        },
                        icon = {
                            Icon(
                                imageVector = category.icon,
                                contentDescription = null,
                                modifier = Modifier.size(24.dp)
                            )
                        },
                        selectedContentColor = when (category) {
                            CalculatorCategory.MATH -> NeonBlue
                            CalculatorCategory.SPECIAL -> NeonPink
                        },
                        unselectedContentColor = NeonText.copy(alpha = 0.6f)
                    )
                }
            }

            // Calculator grid
            LazyVerticalGrid(
                columns = GridCells.Fixed(2),
                contentPadding = PaddingValues(16.dp),
                horizontalArrangement = Arrangement.spacedBy(16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp),
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
            ) {
                items(calculators.filter { it.category == selectedCategory }) { calculator ->
                    ModernCalculatorCard(
                        calculator = calculator,
                        isSelected = selectedCalculator?.id == calculator.id,
                        onClick = {
                            selectedCalculator = calculator
                            FeedbackUtil.buttonPress(context)

                            // Update recently used calculators
                            recentlyUsedCalculators = listOf(calculator) + recentlyUsedCalculators.filter { it != calculator }
                            if (recentlyUsedCalculators.size > 5) {
                                recentlyUsedCalculators = recentlyUsedCalculators.dropLast(1)
                            }

                            // Navigate to the selected calculator
                            onNavigateToCalculator(calculator.id)
                        }
                    )
                }
            }

            // Recently used calculators
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
            ) {
                Text(
                    text = "Recently Used",
                    style = MaterialTheme.typography.titleMedium,
                    color = NeonGlow,
                    modifier = Modifier.padding(bottom = 8.dp)
                )

                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(end = 16.dp)
                ) {
                    items(recentlyUsedCalculators) { calculator ->
                        RecentCalculatorChip(
                            calculator = calculator,
                            onClick = {
                                selectedCategory = calculator.category as CalculatorCategory
                                selectedCalculator = calculator
                                FeedbackUtil.buttonPress(context)

                                // Navigate to the selected calculator
                                onNavigateToCalculator(calculator.id)
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun ModernCalculatorCard(
    calculator: CalculatorType,
    isSelected: Boolean,
    onClick: () -> Unit
) {
    val accentColor = calculator.accentColor
    val cardAlpha = if (isSelected) 0.95f else 0.7f
    val elevation = if (isSelected) 16.dp else 8.dp

    // Animation for selection
    val scale by animateFloatAsState(
        targetValue = if (isSelected) 1.03f else 1f,
        animationSpec = tween(300, easing = EaseOutQuart),
        label = "cardScale"
    )

    // Glow animation
    val infiniteTransition = rememberInfiniteTransition(label = "glowPulse")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.4f,
        targetValue = 0.8f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    Box(
        modifier = Modifier
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
    ) {
        // Glow effect for selected card
        if (isSelected) {
            Box(
                modifier = Modifier
                    .matchParentSize()
                    .shadow(
                        elevation = 20.dp,
                        shape = RoundedCornerShape(20.dp),
                        spotColor = accentColor.copy(alpha = glowAlpha),
                        ambientColor = accentColor.copy(alpha = glowAlpha * 0.5f)
                    )
            )
        }

        // Main card
        Card(
            modifier = Modifier
                .fillMaxWidth()
                .height(160.dp)
                .clickable { onClick() }
                .shadow(
                    elevation = elevation,
                    shape = RoundedCornerShape(20.dp),
                    spotColor = accentColor.copy(alpha = 0.3f),
                    ambientColor = accentColor.copy(alpha = 0.15f)
                ),
            shape = RoundedCornerShape(20.dp),
            colors = CardDefaults.cardColors(
                containerColor = NeonCard.copy(alpha = cardAlpha),
                contentColor = NeonText
            ),
            border = if (isSelected) BorderStroke(2.dp, accentColor.copy(alpha = 0.7f)) else null
        ) {
            Box(
                modifier = Modifier.fillMaxSize()
            ) {
                // Background gradient
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            brush = Brush.verticalGradient(
                                colors = listOf(
                                    NeonCard.copy(alpha = 0.9f),
                                    accentColor.copy(alpha = 0.05f)
                                )
                            )
                        )
                )

                // Content
                Column(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.SpaceBetween
                ) {
                    // Icon with background
                    Box(
                        modifier = Modifier
                            .size(48.dp)
                            .background(
                                color = accentColor.copy(alpha = 0.15f),
                                shape = RoundedCornerShape(12.dp)
                            )
                            .padding(8.dp),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            imageVector = calculator.icon,
                            contentDescription = null,
                            tint = accentColor,
                            modifier = Modifier.size(28.dp)
                        )
                    }

                    // Title and description
                    Column(
                        verticalArrangement = Arrangement.spacedBy(4.dp)
                    ) {
                        Text(
                            text = calculator.title,
                            style = MaterialTheme.typography.titleLarge.copy(
                                fontWeight = FontWeight.Bold
                            ),
                            color = if (isSelected) accentColor else NeonText
                        )

                        Text(
                            text = calculator.description,
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText.copy(alpha = 0.7f),
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

@Composable
private fun RecentCalculatorChip(
    calculator: CalculatorType,
    onClick: () -> Unit
) {
    val accentColor = calculator.accentColor

    Surface(
        onClick = onClick,
        modifier = Modifier.height(40.dp),
        shape = RoundedCornerShape(20.dp),
        color = NeonCard.copy(alpha = 0.7f),
        border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
    ) {
        Row(
            modifier = Modifier.padding(horizontal = 12.dp, vertical = 8.dp),
            horizontalArrangement = Arrangement.spacedBy(8.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = calculator.icon,
                contentDescription = null,
                tint = accentColor,
                modifier = Modifier.size(18.dp)
            )

            Text(
                text = calculator.title,
                style = MaterialTheme.typography.labelMedium,
                color = NeonText
            )
        }
    }
}

@Composable
private fun BasicCalculatorContent(
    viewModel: CalculatorViewModel,
    showSciPad: Boolean,
    onToggleSciPad: () -> Unit,
    modifier: Modifier = Modifier,
    accentColor: Color = NeonBlue
) {
    // Animated background with subtle pulse effect
    val infiniteTransition = rememberInfiniteTransition(label = "calculatorPulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.98f,
        targetValue = 1.02f,
        animationSpec = infiniteRepeatable(
            animation = tween(5000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "calculatorPulseAnimation"
    )

    // Modern, sleek calculator content with glass effect
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(
                Brush.verticalGradient(
                    colors = listOf(
                        NeonDeepBlue.copy(alpha = 0.9f),
                        NeonBackground.copy(alpha = 0.95f)
                    )
                )
            )
    ) {
        // Background glow effect
        Box(
            modifier = Modifier
                .fillMaxSize()
                .graphicsLayer {
                    scaleX = pulseScale
                    scaleY = pulseScale
                }
                .background(
                    Brush.radialGradient(
                        colors = listOf(
                            accentColor.copy(alpha = 0.05f),
                            Color.Transparent
                        ),
                        center = Offset(0.5f, 0.5f),
                        radius = 1000f
                    )
                )
        )

        // Main content
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // Display with enhanced visual appeal
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .shadow(
                        elevation = 16.dp,
                        spotColor = accentColor.copy(alpha = 0.2f),
                        ambientColor = accentColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(24.dp)
                    ),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                ),
                border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
            ) {
                CalculatorDisplay(
                    expression = viewModel.state.collectAsState().value.display,
                    result = viewModel.state.collectAsState().value.result,
                    isError = viewModel.state.collectAsState().value.isError,
                    onExpressionChange = viewModel::onInput,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                )
            }

            // Scientific pad toggle with improved styling
            if (onToggleSciPad !== {}) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.End
                ) {
                    Surface(
                        onClick = onToggleSciPad,
                        modifier = Modifier
                            .shadow(
                                elevation = 8.dp,
                                spotColor = accentColor.copy(alpha = 0.2f),
                                ambientColor = accentColor.copy(alpha = 0.1f),
                                shape = RoundedCornerShape(20.dp)
                            ),
                        shape = RoundedCornerShape(20.dp),
                        color = NeonCard.copy(alpha = 0.8f),
                        border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
                    ) {
                        Row(
                            modifier = Modifier.padding(horizontal = 16.dp, vertical = 8.dp),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = if (showSciPad) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                contentDescription = null,
                                tint = accentColor
                            )
                            Text(
                                text = if (showSciPad) "Hide Scientific" else "Show Scientific",
                                style = MaterialTheme.typography.labelLarge,
                                color = NeonText
                            )
                        }
                    }
                }
            }

            // Scientific pad with improved styling
            AnimatedVisibility(
                visible = showSciPad,
                enter = fadeIn() + expandVertically(),
                exit = fadeOut() + shrinkVertically()
            ) {
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .shadow(
                            elevation = 12.dp,
                            spotColor = accentColor.copy(alpha = 0.15f),
                            ambientColor = accentColor.copy(alpha = 0.08f),
                            shape = RoundedCornerShape(20.dp)
                        ),
                    shape = RoundedCornerShape(20.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = NeonCard.copy(alpha = 0.85f)
                    ),
                    border = BorderStroke(1.dp, accentColor.copy(alpha = 0.2f))
                ) {
                    ScientificPad(
                        onFunctionClick = viewModel::onScientificFunction,
                        accentColor = accentColor,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp)
                    )
                }
            }

            // Main calculator pad with improved styling
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .weight(1f)
                    .shadow(
                        elevation = 16.dp,
                        spotColor = accentColor.copy(alpha = 0.2f),
                        ambientColor = accentColor.copy(alpha = 0.1f),
                        shape = RoundedCornerShape(24.dp)
                    ),
                shape = RoundedCornerShape(24.dp),
                colors = CardDefaults.cardColors(
                    containerColor = NeonCard.copy(alpha = 0.9f)
                ),
                border = BorderStroke(1.dp, accentColor.copy(alpha = 0.3f))
            ) {
                CalculatorPad(
                    onNumberClick = viewModel::onNumber,
                    onOperatorClick = viewModel::onOperator,
                    onDeleteClick = viewModel::onDelete,
                    onClearClick = viewModel::onClear,
                    onEqualsClick = viewModel::onEquals,
                    accentColor = accentColor,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(12.dp)
                )
            }
        }
    }
}
