package com.app.wordifynumbers.ui.screens

import androidx.compose.animation.*
import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.SolidColor
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.platform.ClipboardManager
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.app.wordifynumbers.model.NumberBreakdown
import com.app.wordifynumbers.model.ScaleComponent
import com.app.wordifynumbers.model.VisualizationUnit
import com.app.wordifynumbers.ui.components.*
import com.app.wordifynumbers.ui.theme.*
import com.app.wordifynumbers.ui.viewmodel.LargeNumbersViewModel
import com.app.wordifynumbers.ui.navigation.SimpleBackHandler
import com.app.wordifynumbers.util.FeedbackUtil
import kotlinx.coroutines.delay

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LargeNumbersEducationScreen(
    modifier: Modifier = Modifier,
    viewModel: LargeNumbersViewModel = viewModel(),
    onNavigateToWords: () -> Unit = {},
    onNavigateToCalculator: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val clipboardManager = LocalClipboardManager.current
    val scrollState = rememberScrollState()

    // Handle back button press using simple handler
    SimpleBackHandler()

    // State for language dropdown
    var languageDropdownExpanded by remember { mutableStateOf(false) }

    // Animation for the background
    val infiniteTransition = rememberInfiniteTransition(label = "backgroundPulse")
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.95f,
        targetValue = 1.05f,
        animationSpec = infiniteRepeatable(
            animation = tween(8000, easing = EaseInOutCubic),
            repeatMode = RepeatMode.Reverse
        ),
        label = "pulseAnimation"
    )

    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                color = NeonBackground
            )
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
                .verticalScroll(scrollState),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // New Wordify Header
            WordifyHeader(
                showScreenTitle = true,
                screenTitle = "Large Numbers Education",
                screenIcon = Icons.Default.School,
                actions = {
                    // Words navigation
                    WordifyHeaderAction(
                        icon = Icons.Default.FormatListNumbered,
                        contentDescription = "Go to Words",
                        onClick = {
                            onNavigateToWords()
                            FeedbackUtil.buttonPress(context)
                        }
                    )

                    // Calculator navigation
                    WordifyHeaderAction(
                        icon = Icons.Default.Calculate,
                        contentDescription = "Go to Calculators",
                        onClick = {
                            onNavigateToCalculator("basic")
                            FeedbackUtil.buttonPress(context)
                        }
                    )


                    // Reset button
                    WordifyHeaderAction(
                        icon = Icons.Default.Refresh,
                        contentDescription = "Reset",
                        onClick = {
                            viewModel.reset()
                            FeedbackUtil.buttonPress(context)
                        }
                    )
                }
            )

            // Feature introduction card
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Understand Large Numbers",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGlow
                    )

                    Text(
                        text = "Explore and visualize large numbers from thousands to quintillions. Learn how they break down, what they mean, and how they compare to real-world examples.",
                        style = MaterialTheme.typography.bodyMedium,
                        color = NeonText
                    )
                }
            }

            // Scale selection card
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    Text(
                        text = "Select Number Scale",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGlow
                    )

                    // Beautiful scale cards in a grid layout
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(3),
                        horizontalArrangement = Arrangement.spacedBy(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        modifier = Modifier.height(200.dp)
                    ) {
                        items(viewModel.predefinedScales.size) { index ->
                            val (name, value) = viewModel.predefinedScales[index]
                            val zeros = when(name) {
                                "Thousand" -> "1,000 (3 zeros)"
                                "Million" -> "1,000,000 (6 zeros)"
                                "Billion" -> "1,000,000,000 (9 zeros)"
                                "Trillion" -> "1,000,000,000,000 (12 zeros)"
                                "Quadrillion" -> "1,000,000,000,000,000 (15 zeros)"
                                "Quintillion" -> "1,000,000,000,000,000,000 (18 zeros)"
                                else -> ""
                            }

                            // Animated scale card
                            NumberScaleCard(
                                name = name,
                                zeros = zeros,
                                onClick = {
                                    viewModel.selectPredefinedScale(value)
                                    FeedbackUtil.buttonPress(context)
                                }
                            )
                        }
                    }

                    // Main title with large font
                    Text(
                        text = "ENTER YOUR NUMBER",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 1.sp
                        ),
                        color = NeonGlow,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 16.dp),
                        textAlign = TextAlign.Center
                    )

                    // Enhanced custom number input
                    OutlinedTextField(
                        value = viewModel.numberInput,
                        onValueChange = {
                            // Allow any number of digits (no limit)
                            if (it.isEmpty() || it.all { char -> char.isDigit() }) {
                                viewModel.updateNumberInput(it)
                                FeedbackUtil.buttonPress(context)
                            }
                        },
                        label = { Text("Enter any large number") },
                        placeholder = { Text("Example: 1000000000") },
                        keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Number),
                        modifier = Modifier
                            .fillMaxWidth()
                            .height(90.dp) // Larger height
                            .shadow(
                                elevation = 12.dp,
                                spotColor = NeonGlow.copy(alpha = 0.3f),
                                ambientColor = NeonGlow.copy(alpha = 0.2f),
                                shape = RoundedCornerShape(16.dp)
                            ),
                        singleLine = true,
                        maxLines = 1,
                        textStyle = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Medium
                        ),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = NeonGlow,
                            unfocusedBorderColor = NeonGlow.copy(alpha = 0.5f),
                            focusedLabelColor = NeonGlow,
                            unfocusedLabelColor = NeonGlow.copy(alpha = 0.7f),
                            cursorColor = NeonGlow,
                            focusedTextColor = NeonText,
                            unfocusedTextColor = NeonText,
                            focusedPlaceholderColor = NeonText.copy(alpha = 0.7f),
                            unfocusedPlaceholderColor = NeonText.copy(alpha = 0.5f),
                            // Enhanced container colors
                            focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                            unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                        ),
                        leadingIcon = {
                            Icon(
                                imageVector = Icons.Default.Numbers,
                                contentDescription = null,
                                tint = NeonGlow,
                                modifier = Modifier.size(36.dp) // Larger icon
                            )
                        }
                    )

                    // Learn button
                    Button(
                        onClick = {
                            // The processing happens automatically in the ViewModel
                            FeedbackUtil.buttonPress(context)
                        },
                        colors = ButtonDefaults.buttonColors(
                            containerColor = NeonGlow,
                            contentColor = NeonBackground
                        ),
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Icon(
                            imageVector = Icons.Default.School,
                            contentDescription = null,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text("Learn About This Number")
                    }
                }
            }

            // Language selection card
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(16.dp)
                ) {
                    // Main title with large font
                    Text(
                        text = "SELECT YOUR LANGUAGE",
                        style = MaterialTheme.typography.headlineMedium.copy(
                            fontWeight = FontWeight.Bold,
                            letterSpacing = 1.sp
                        ),
                        color = NeonGlow,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(vertical = 8.dp),
                        textAlign = TextAlign.Center
                    )

                    // Enhanced language dropdown
                    Box(
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        OutlinedTextField(
                            value = viewModel.selectedLanguage,
                            onValueChange = { },
                            readOnly = true,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(90.dp) // Larger height
                                .shadow(
                                    elevation = 12.dp,
                                    spotColor = NeonGlow.copy(alpha = 0.3f),
                                    ambientColor = NeonGlow.copy(alpha = 0.2f),
                                    shape = RoundedCornerShape(16.dp)
                                ),
                            label = { Text("Choose a language") },
                            trailingIcon = {
                                IconButton(onClick = { languageDropdownExpanded = !languageDropdownExpanded }) {
                                    Icon(
                                        imageVector = if (languageDropdownExpanded) Icons.Default.KeyboardArrowUp else Icons.Default.KeyboardArrowDown,
                                        contentDescription = "Select language",
                                        tint = NeonGlow,
                                        modifier = Modifier.size(32.dp) // Larger icon
                                    )
                                }
                            },
                            textStyle = MaterialTheme.typography.headlineMedium.copy(
                                fontWeight = FontWeight.Medium
                            ),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedBorderColor = NeonGlow,
                                unfocusedBorderColor = NeonGlow.copy(alpha = 0.5f),
                                focusedLabelColor = NeonGlow,
                                unfocusedLabelColor = NeonGlow.copy(alpha = 0.7f),
                                focusedTextColor = NeonText,
                                unfocusedTextColor = NeonText,
                                // Enhanced container colors
                                focusedContainerColor = NeonCard.copy(alpha = 0.95f),
                                unfocusedContainerColor = NeonCard.copy(alpha = 0.8f)
                            ),
                            leadingIcon = {
                                Icon(
                                    imageVector = Icons.Default.Language,
                                    contentDescription = null,
                                    tint = NeonGlow,
                                    modifier = Modifier.size(36.dp) // Larger icon
                                )
                            }
                        )

                        DropdownMenu(
                            expanded = languageDropdownExpanded,
                            onDismissRequest = { languageDropdownExpanded = false },
                            modifier = Modifier
                                .fillMaxWidth(0.9f)
                                .background(NeonCard)
                        ) {
                            viewModel.supportedLanguages.forEach { (name, locale) ->
                                DropdownMenuItem(
                                    text = { Text(name) },
                                    onClick = {
                                        viewModel.updateLanguage(name, locale)
                                        languageDropdownExpanded = false
                                        FeedbackUtil.buttonPress(context)
                                    },
                                    colors = MenuDefaults.itemColors(
                                        textColor = if (viewModel.selectedLanguage == name) NeonGlow else NeonText
                                    )
                                )
                            }
                        }
                    }
                }
            }

            // Number breakdown section (only visible when a number is entered)
            viewModel.numberBreakdown?.let { breakdown ->
                NeonCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Analytics,
                                contentDescription = null,
                                tint = NeonGlow
                            )

                            Text(
                                text = "Number Breakdown",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )
                        }

                        // Display formats
                        FormatDisplay(breakdown)

                        Divider(color = NeonGlow.copy(alpha = 0.3f))

                        // Component breakdown
                        Text(
                            text = "Components:",
                            style = MaterialTheme.typography.titleSmall,
                            color = NeonGlow
                        )

                        breakdown.components.forEach { component ->
                            ComponentRow(component)
                        }
                    }
                }

                // Visualization section
                NeonCard(
                    modifier = Modifier.fillMaxWidth()
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(16.dp),
                        verticalArrangement = Arrangement.spacedBy(12.dp)
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            horizontalArrangement = Arrangement.spacedBy(8.dp)
                        ) {
                            Icon(
                                imageVector = Icons.Default.Visibility,
                                contentDescription = null,
                                tint = NeonGlow
                            )

                            Text(
                                text = "Visualization & Context",
                                style = MaterialTheme.typography.titleMedium,
                                color = NeonGlow
                            )
                        }

                        // Explanation text
                        Text(
                            text = viewModel.explanationText,
                            style = MaterialTheme.typography.bodyMedium,
                            color = NeonText
                        )

                        Divider(color = NeonGlow.copy(alpha = 0.3f))

                        // Visualization units
                        viewModel.visualizationUnits.forEach { unit ->
                            VisualizationRow(unit)
                        }
                    }
                }
            }

            // Educational tips card
            NeonCard(
                modifier = Modifier.fillMaxWidth()
            ) {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = "Tips & Information",
                        style = MaterialTheme.typography.titleMedium,
                        color = NeonGlow
                    )

                    Divider(
                        color = NeonGlow.copy(alpha = 0.3f),
                        modifier = Modifier.padding(vertical = 4.dp)
                    )

                    Row(
                        verticalAlignment = Alignment.Top,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Info,
                            contentDescription = null,
                            tint = NeonGlow.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )

                        Text(
                            text = "Different cultures have different ways of naming large numbers. For example, in the US a billion is 10^9, while in some European countries it traditionally meant 10^12.",
                            style = MaterialTheme.typography.bodySmall,
                            color = NeonText
                        )
                    }

                    Row(
                        verticalAlignment = Alignment.Top,
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Lightbulb,
                            contentDescription = null,
                            tint = NeonGlow.copy(alpha = 0.7f),
                            modifier = Modifier.size(16.dp)
                        )

                        Text(
                            text = "The largest named number in common use is a googol (10^100), which is larger than the estimated number of atoms in the observable universe (10^80).",
                            style = MaterialTheme.typography.bodySmall,
                            color = NeonText
                        )
                    }
                }
            }

            // Footer space
            Spacer(modifier = Modifier.height(60.dp))
        }
    }
}

@Composable
private fun FormatDisplay(breakdown: NumberBreakdown) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(NeonGlow.copy(alpha = 0.1f))
            .padding(12.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "International:",
                style = MaterialTheme.typography.bodyMedium,
                color = NeonText.copy(alpha = 0.7f)
            )
            Text(
                text = breakdown.internationalFormat,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                color = NeonGlow
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "Scientific:",
                style = MaterialTheme.typography.bodyMedium,
                color = NeonText.copy(alpha = 0.7f)
            )
            Text(
                text = breakdown.scientificNotation,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                color = NeonGlow
            )
        }

        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.SpaceBetween
        ) {
            Text(
                text = "Localized:",
                style = MaterialTheme.typography.bodyMedium,
                color = NeonText.copy(alpha = 0.7f)
            )
            Text(
                text = breakdown.localizedFormat,
                style = MaterialTheme.typography.bodyMedium.copy(fontWeight = FontWeight.Bold),
                color = NeonGlow
            )
        }
    }
}

@Composable
private fun ComponentRow(component: ScaleComponent) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 4.dp),
        horizontalArrangement = Arrangement.SpaceBetween,
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = component.value.toString(),
            style = MaterialTheme.typography.bodyLarge.copy(fontWeight = FontWeight.Bold),
            color = NeonGlow
        )

        Text(
            text = component.localizedName,
            style = MaterialTheme.typography.bodyMedium,
            color = NeonText
        )
    }
}

@Composable
private fun VisualizationRow(unit: VisualizationUnit) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .clip(RoundedCornerShape(8.dp))
            .background(NeonGlow.copy(alpha = 0.05f))
            .border(1.dp, NeonGlow.copy(alpha = 0.2f), RoundedCornerShape(8.dp))
            .padding(12.dp),
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Text(
            text = unit.label,
            style = MaterialTheme.typography.titleMedium.copy(fontWeight = FontWeight.Bold),
            color = NeonGlow
        )

        Text(
            text = unit.description,
            style = MaterialTheme.typography.bodyMedium,
            color = NeonText
        )

        Text(
            text = "Example: ${unit.comparisonExample}",
            style = MaterialTheme.typography.bodySmall,
            color = NeonText.copy(alpha = 0.8f)
        )
    }
}

@Composable
private fun NumberScaleCard(
    name: String,
    zeros: String,
    onClick: () -> Unit
) {
    // Animation states
    val infiniteTransition = rememberInfiniteTransition(label = "cardAnimation")
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.6f,
        targetValue = 1f,
        animationSpec = infiniteRepeatable(
            animation = tween(1500, easing = EaseInOutSine),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAnimation"
    )

    var isPressed by remember { mutableStateOf(false) }
    val scale by animateFloatAsState(
        targetValue = if (isPressed) 0.95f else 1f,
        animationSpec = tween(
            durationMillis = 100,
            easing = FastOutSlowInEasing
        ),
        label = "scaleAnimation"
    )

    // Reset pressed state after animation
    if (isPressed) {
        LaunchedEffect(isPressed) {
            delay(100)
            isPressed = false
        }
    }

    // Card with glow effect
    Box(
        modifier = Modifier
            .fillMaxWidth()
            .aspectRatio(0.8f)
            .graphicsLayer {
                scaleX = scale
                scaleY = scale
            }
            .shadow(
                elevation = 8.dp,
                spotColor = NeonGlow.copy(alpha = if (isPressed) 0.8f else 0.2f),
                ambientColor = NeonGlow.copy(alpha = if (isPressed) 0.5f else 0.1f),
                shape = RoundedCornerShape(16.dp)
            )
            .clip(RoundedCornerShape(16.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonCard.copy(alpha = 0.8f),
                        NeonCard.copy(alpha = 0.9f)
                    )
                )
            )
            .border(
                width = 1.dp,
                brush = Brush.verticalGradient(
                    colors = listOf(
                        NeonGlow.copy(alpha = 0.3f),
                        NeonGlow.copy(alpha = 0.5f)
                    )
                ),
                shape = RoundedCornerShape(16.dp)
            )
            .clickable {
                isPressed = true
                onClick()
            }
    ) {
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(12.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // Scale name with glow effect
            Text(
                text = name,
                style = MaterialTheme.typography.titleMedium.copy(
                    fontWeight = FontWeight.Bold
                ),
                color = NeonGlow.copy(alpha = glowAlpha),
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(8.dp))

            // Zeros information
            Text(
                text = zeros,
                style = MaterialTheme.typography.bodySmall,
                color = NeonText,
                textAlign = TextAlign.Center
            )
        }
    }
}
