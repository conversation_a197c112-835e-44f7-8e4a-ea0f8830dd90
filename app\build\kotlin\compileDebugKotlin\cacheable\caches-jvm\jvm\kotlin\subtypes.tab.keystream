#androidx.activity.ComponentActivitykotlin.Annotation,com.app.wordifynumbers.ui.screens.DateResult,androidx.lifecycle.ViewModelProvider.Factoryandroidx.lifecycle.ViewModel2kotlinx.serialization.internal.GeneratedSerializerkotlin.Enum"androidx.datastore.core.Serializer1com.app.wordifynumbers.data.SimpleFinanceDatabase1com.app.wordifynumbers.data.SimpleFinanceEntryDao(com.app.wordifynumbers.data.RoomDatabase1com.app.wordifynumbers.data.RoomDatabase.Callback+androidx.lifecycle.DefaultLifecycleObserver,androidx.compose.material.ripple.RippleTheme#androidx.lifecycle.AndroidViewModel-com.app.wordifynumbers.util.CalculationResult.com.app.wordifynumbers.util.ExchangeRateResult4com.app.wordifynumbers.util.CurrencyValidationResult,com.app.wordifynumbers.util.ValidationResult                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      