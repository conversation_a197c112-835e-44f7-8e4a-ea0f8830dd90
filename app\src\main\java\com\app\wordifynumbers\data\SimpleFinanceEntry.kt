package com.app.wordifynumbers.data

// Temporary import from stubs while Room KSP issue is resolved
// import androidx.room.Entity
// import androidx.room.PrimaryKey
import java.util.UUID

/**
 * Entity representing a simple finance entry
 * Designed for lightweight tracking of income and expenses
 */
@Entity(tableName = "finance_entries")
data class SimpleFinanceEntry(
    @PrimaryKey
    val id: String = generateSafeUUID(),
    val type: EntryType,
    val amount: Double,
    val category: String,
    val note: String = "",
    val date: Long = System.currentTimeMillis()
) {
    companion object {
        /**
         * Generate a safe UUID string that won't cause issues with database operations
         */
        fun generateSafeUUID(): String {
            return try {
                UUID.randomUUID().toString()
            } catch (e: Exception) {
                // Fallback to timestamp-based ID if UUID generation fails
                "entry-${System.currentTimeMillis()}-${(Math.random() * 10000).toInt()}"
            }
        }
    }
}

/**
 * Enum representing the type of finance entry
 */
enum class EntryType {
    INCOME,
    EXPENSE,
    TRANSFER
}
